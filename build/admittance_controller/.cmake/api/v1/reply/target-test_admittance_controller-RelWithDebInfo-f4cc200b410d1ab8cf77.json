{"artifacts": [{"path": "test_admittance_controller"}], "backtrace": 2, "backtraceGraph": {"commands": ["add_executable", "add_rostest_with_parameters_gmock", "target_link_libraries", "ament_target_dependencies", "set_target_properties", "include", "find_package", "generate_parameter_library", "add_compile_options", "target_include_directories"], "files": ["/opt/ros/humble/share/generate_parameter_library/cmake/generate_parameter_library.cmake", "CMakeLists.txt", "/opt/ros/humble/share/ament_cmake_target_dependencies/cmake/ament_target_dependencies.cmake", "/opt/ros/humble/src/gmock_vendor/CMakeLists.txt", "/home/<USER>/code/ros2_control/install/share/control_msgs/cmake/export_control_msgs__rosidl_typesupport_fastrtps_cExport.cmake", "/home/<USER>/code/ros2_control/install/share/control_msgs/cmake/ament_cmake_export_targets-extras.cmake", "/home/<USER>/code/ros2_control/install/share/control_msgs/cmake/control_msgsConfig.cmake", "/home/<USER>/code/ros2_control/install/share/control_msgs/cmake/export_control_msgs__rosidl_typesupport_fastrtps_cppExport.cmake", "/home/<USER>/code/ros2_control/install/share/control_msgs/cmake/control_msgs__rosidl_typesupport_introspection_cExport.cmake", "/home/<USER>/code/ros2_control/install/share/control_msgs/cmake/control_msgs__rosidl_typesupport_introspection_cppExport.cmake", "/home/<USER>/code/ros2_control/install/share/control_msgs/cmake/control_msgs__rosidl_typesupport_cppExport.cmake", "/home/<USER>/code/ros2_control/install/share/control_msgs/cmake/export_control_msgs__rosidl_generator_pyExport.cmake", "/home/<USER>/code/ros2_control/install/share/control_msgs/cmake/control_msgs__rosidl_typesupport_cExport.cmake", "/home/<USER>/code/ros2_control/install/share/control_msgs/cmake/export_control_msgs__rosidl_generator_cExport.cmake", "/home/<USER>/code/ros2_control/install/share/control_toolbox/cmake/export_control_toolboxExport.cmake", "/home/<USER>/code/ros2_control/install/share/control_toolbox/cmake/ament_cmake_export_targets-extras.cmake", "/home/<USER>/code/ros2_control/install/share/control_toolbox/cmake/control_toolboxConfig.cmake", "/home/<USER>/code/ros2_control/install/share/hardware_interface/cmake/export_hardware_interfaceExport.cmake", "/home/<USER>/code/ros2_control/install/share/hardware_interface/cmake/ament_cmake_export_targets-extras.cmake", "/home/<USER>/code/ros2_control/install/share/hardware_interface/cmake/hardware_interfaceConfig.cmake", "/home/<USER>/code/ros2_control/install/share/controller_interface/cmake/ament_cmake_export_dependencies-extras.cmake", "/home/<USER>/code/ros2_control/install/share/controller_interface/cmake/controller_interfaceConfig.cmake", "/opt/ros/humble/share/pluginlib/cmake/export_pluginlibExport.cmake", "/opt/ros/humble/share/pluginlib/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/pluginlib/cmake/pluginlibConfig.cmake", "/opt/ros/humble/share/filters/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/filters/cmake/filtersConfig.cmake", "/home/<USER>/code/ros2_control/install/share/control_toolbox/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/rclcpp_lifecycle/cmake/rclcpp_lifecycleExport.cmake", "/opt/ros/humble/share/rclcpp_lifecycle/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rclcpp_lifecycle/cmake/rclcpp_lifecycleConfig.cmake", "/home/<USER>/code/ros2_control/install/share/realtime_tools/cmake/export_realtime_toolsExport.cmake", "/home/<USER>/code/ros2_control/install/share/realtime_tools/cmake/ament_cmake_export_targets-extras.cmake", "/home/<USER>/code/ros2_control/install/share/realtime_tools/cmake/realtime_toolsConfig.cmake", "/opt/ros/humble/share/tf2_geometry_msgs/cmake/export_tf2_geometry_msgsExport.cmake", "/opt/ros/humble/share/tf2_geometry_msgs/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/tf2_geometry_msgs/cmake/tf2_geometry_msgsConfig.cmake", "/opt/ros/humble/share/class_loader/cmake/class_loaderExport.cmake", "/opt/ros/humble/share/class_loader/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/class_loader/cmake/class_loaderConfig.cmake", "/opt/ros/humble/share/pluginlib/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/rclcpp_action/cmake/rclcpp_actionExport.cmake", "/opt/ros/humble/share/rclcpp_action/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rclcpp_action/cmake/rclcpp_actionConfig.cmake", "/home/<USER>/code/ros2_control/install/share/realtime_tools/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/tf2_ros/cmake/export_tf2_rosExport.cmake", "/opt/ros/humble/share/tf2_ros/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/tf2_ros/cmake/tf2_rosConfig.cmake", "/opt/ros/humble/share/tf2_eigen/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/tf2_eigen/cmake/tf2_eigenConfig.cmake", "/opt/ros/humble/share/rclcpp/cmake/rclcppExport.cmake", "/opt/ros/humble/share/rclcpp/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rclcpp/cmake/rclcppConfig.cmake", "/opt/ros/humble/share/parameter_traits/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/parameter_traits/cmake/parameter_traitsConfig.cmake", "/opt/ros/humble/share/rcl/cmake/rclExport.cmake", "/opt/ros/humble/share/rcl/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rcl/cmake/rclConfig.cmake", "/opt/ros/humble/share/libstatistics_collector/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/libstatistics_collector/cmake/libstatistics_collectorConfig.cmake", "/opt/ros/humble/share/rclcpp/cmake/ament_cmake_export_dependencies-extras.cmake", "/usr/lib/x86_64-linux-gnu/cmake/fmt/fmt-targets.cmake", "/usr/lib/x86_64-linux-gnu/cmake/fmt/fmt-config.cmake", "/opt/ros/humble/share/rcl_yaml_param_parser/cmake/rcl_yaml_param_parserExport.cmake", "/opt/ros/humble/share/rcl_yaml_param_parser/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rcl_yaml_param_parser/cmake/rcl_yaml_param_parserConfig.cmake", "/opt/ros/humble/share/rcl/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/rosidl_typesupport_cpp/cmake/rosidl_typesupport_cppExport.cmake", "/opt/ros/humble/share/rosidl_typesupport_cpp/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rosidl_typesupport_cpp/cmake/rosidl_typesupport_cppConfig.cmake", "/opt/ros/humble/share/builtin_interfaces/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/builtin_interfaces/cmake/builtin_interfacesConfig.cmake", "/opt/ros/humble/share/action_msgs/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/action_msgs/cmake/action_msgsConfig.cmake", "/home/<USER>/code/ros2_control/install/share/control_msgs/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/rcutils/cmake/rcutilsExport.cmake", "/opt/ros/humble/share/rcutils/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rcutils/cmake/rcutilsConfig.cmake", "/opt/ros/humble/share/rosidl_runtime_c/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/rosidl_runtime_c/cmake/rosidl_runtime_cConfig.cmake"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 80, "parent": 0}, {"command": 0, "file": 0, "line": 164, "parent": 1}, {"command": 2, "file": 0, "line": 167, "parent": 1}, {"command": 2, "file": 1, "line": 84, "parent": 0}, {"command": 3, "file": 1, "line": 85, "parent": 0}, {"command": 2, "file": 2, "line": 145, "parent": 5}, {"file": 3}, {"command": 2, "file": 3, "line": 25, "parent": 7}, {"command": 3, "file": 1, "line": 52, "parent": 0}, {"command": 2, "file": 2, "line": 145, "parent": 9}, {"command": 6, "file": 1, "line": 32, "parent": 0}, {"file": 6, "parent": 11}, {"command": 5, "file": 6, "line": 41, "parent": 12}, {"file": 5, "parent": 13}, {"command": 5, "file": 5, "line": 9, "parent": 14}, {"file": 4, "parent": 15}, {"command": 4, "file": 4, "line": 61, "parent": 16}, {"command": 5, "file": 5, "line": 9, "parent": 14}, {"file": 7, "parent": 18}, {"command": 4, "file": 7, "line": 61, "parent": 19}, {"command": 5, "file": 5, "line": 9, "parent": 14}, {"file": 8, "parent": 21}, {"command": 4, "file": 8, "line": 61, "parent": 22}, {"command": 5, "file": 5, "line": 9, "parent": 14}, {"file": 9, "parent": 24}, {"command": 4, "file": 9, "line": 61, "parent": 25}, {"command": 5, "file": 5, "line": 9, "parent": 14}, {"file": 10, "parent": 27}, {"command": 4, "file": 10, "line": 61, "parent": 28}, {"command": 5, "file": 5, "line": 9, "parent": 14}, {"file": 11, "parent": 30}, {"command": 4, "file": 11, "line": 61, "parent": 31}, {"command": 5, "file": 5, "line": 9, "parent": 14}, {"file": 12, "parent": 33}, {"command": 4, "file": 12, "line": 61, "parent": 34}, {"command": 5, "file": 5, "line": 9, "parent": 14}, {"file": 13, "parent": 36}, {"command": 4, "file": 13, "line": 61, "parent": 37}, {"command": 6, "file": 1, "line": 32, "parent": 0}, {"file": 16, "parent": 39}, {"command": 5, "file": 16, "line": 41, "parent": 40}, {"file": 15, "parent": 41}, {"command": 5, "file": 15, "line": 9, "parent": 42}, {"file": 14, "parent": 43}, {"command": 4, "file": 14, "line": 70, "parent": 44}, {"command": 7, "file": 1, "line": 36, "parent": 0}, {"command": 2, "file": 0, "line": 92, "parent": 46}, {"command": 6, "file": 1, "line": 32, "parent": 0}, {"file": 21, "parent": 48}, {"command": 5, "file": 21, "line": 41, "parent": 49}, {"file": 20, "parent": 50}, {"command": 6, "file": 20, "line": 21, "parent": 51}, {"file": 19, "parent": 52}, {"command": 5, "file": 19, "line": 41, "parent": 53}, {"file": 18, "parent": 54}, {"command": 5, "file": 18, "line": 9, "parent": 55}, {"file": 17, "parent": 56}, {"command": 4, "file": 17, "line": 61, "parent": 57}, {"command": 5, "file": 16, "line": 41, "parent": 40}, {"file": 27, "parent": 59}, {"command": 6, "file": 27, "line": 21, "parent": 60}, {"file": 26, "parent": 61}, {"command": 5, "file": 26, "line": 41, "parent": 62}, {"file": 25, "parent": 63}, {"command": 6, "file": 25, "line": 21, "parent": 64}, {"file": 24, "parent": 65}, {"command": 5, "file": 24, "line": 41, "parent": 66}, {"file": 23, "parent": 67}, {"command": 5, "file": 23, "line": 9, "parent": 68}, {"file": 22, "parent": 69}, {"command": 4, "file": 22, "line": 56, "parent": 70}, {"command": 6, "file": 27, "line": 21, "parent": 60}, {"file": 30, "parent": 72}, {"command": 5, "file": 30, "line": 41, "parent": 73}, {"file": 29, "parent": 74}, {"command": 5, "file": 29, "line": 9, "parent": 75}, {"file": 28, "parent": 76}, {"command": 4, "file": 28, "line": 56, "parent": 77}, {"command": 6, "file": 27, "line": 21, "parent": 60}, {"file": 33, "parent": 79}, {"command": 5, "file": 33, "line": 41, "parent": 80}, {"file": 32, "parent": 81}, {"command": 5, "file": 32, "line": 9, "parent": 82}, {"file": 31, "parent": 83}, {"command": 4, "file": 31, "line": 61, "parent": 84}, {"command": 6, "file": 1, "line": 32, "parent": 0}, {"file": 36, "parent": 86}, {"command": 5, "file": 36, "line": 41, "parent": 87}, {"file": 35, "parent": 88}, {"command": 5, "file": 35, "line": 9, "parent": 89}, {"file": 34, "parent": 90}, {"command": 4, "file": 34, "line": 56, "parent": 91}, {"command": 5, "file": 24, "line": 41, "parent": 66}, {"file": 40, "parent": 93}, {"command": 6, "file": 40, "line": 21, "parent": 94}, {"file": 39, "parent": 95}, {"command": 5, "file": 39, "line": 41, "parent": 96}, {"file": 38, "parent": 97}, {"command": 5, "file": 38, "line": 9, "parent": 98}, {"file": 37, "parent": 99}, {"command": 4, "file": 37, "line": 56, "parent": 100}, {"command": 5, "file": 33, "line": 41, "parent": 80}, {"file": 44, "parent": 102}, {"command": 6, "file": 44, "line": 21, "parent": 103}, {"file": 43, "parent": 104}, {"command": 5, "file": 43, "line": 41, "parent": 105}, {"file": 42, "parent": 106}, {"command": 5, "file": 42, "line": 9, "parent": 107}, {"file": 41, "parent": 108}, {"command": 4, "file": 41, "line": 56, "parent": 109}, {"command": 6, "file": 1, "line": 32, "parent": 0}, {"file": 49, "parent": 111}, {"command": 5, "file": 49, "line": 41, "parent": 112}, {"file": 48, "parent": 113}, {"command": 6, "file": 48, "line": 21, "parent": 114}, {"file": 47, "parent": 115}, {"command": 5, "file": 47, "line": 41, "parent": 116}, {"file": 46, "parent": 117}, {"command": 5, "file": 46, "line": 9, "parent": 118}, {"file": 45, "parent": 119}, {"command": 4, "file": 45, "line": 56, "parent": 120}, {"command": 6, "file": 27, "line": 21, "parent": 60}, {"file": 54, "parent": 122}, {"command": 5, "file": 54, "line": 41, "parent": 123}, {"file": 53, "parent": 124}, {"command": 6, "file": 53, "line": 21, "parent": 125}, {"file": 52, "parent": 126}, {"command": 5, "file": 52, "line": 41, "parent": 127}, {"file": 51, "parent": 128}, {"command": 5, "file": 51, "line": 9, "parent": 129}, {"file": 50, "parent": 130}, {"command": 4, "file": 50, "line": 56, "parent": 131}, {"command": 5, "file": 52, "line": 41, "parent": 127}, {"file": 60, "parent": 133}, {"command": 6, "file": 60, "line": 21, "parent": 134}, {"file": 59, "parent": 135}, {"command": 5, "file": 59, "line": 41, "parent": 136}, {"file": 58, "parent": 137}, {"command": 6, "file": 58, "line": 21, "parent": 138}, {"file": 57, "parent": 139}, {"command": 5, "file": 57, "line": 41, "parent": 140}, {"file": 56, "parent": 141}, {"command": 5, "file": 56, "line": 9, "parent": 142}, {"file": 55, "parent": 143}, {"command": 4, "file": 55, "line": 56, "parent": 144}, {"command": 6, "file": 27, "line": 21, "parent": 60}, {"file": 62, "parent": 146}, {"command": 5, "file": 62, "line": 37, "parent": 147}, {"file": 61, "parent": 148}, {"command": 4, "file": 61, "line": 66, "parent": 149}, {"command": 5, "file": 57, "line": 41, "parent": 140}, {"file": 66, "parent": 151}, {"command": 6, "file": 66, "line": 21, "parent": 152}, {"file": 65, "parent": 153}, {"command": 5, "file": 65, "line": 41, "parent": 154}, {"file": 64, "parent": 155}, {"command": 5, "file": 64, "line": 9, "parent": 156}, {"file": 63, "parent": 157}, {"command": 4, "file": 63, "line": 56, "parent": 158}, {"command": 5, "file": 6, "line": 41, "parent": 12}, {"file": 74, "parent": 160}, {"command": 6, "file": 74, "line": 21, "parent": 161}, {"file": 73, "parent": 162}, {"command": 5, "file": 73, "line": 41, "parent": 163}, {"file": 72, "parent": 164}, {"command": 6, "file": 72, "line": 21, "parent": 165}, {"file": 71, "parent": 166}, {"command": 5, "file": 71, "line": 41, "parent": 167}, {"file": 70, "parent": 168}, {"command": 6, "file": 70, "line": 21, "parent": 169}, {"file": 69, "parent": 170}, {"command": 5, "file": 69, "line": 41, "parent": 171}, {"file": 68, "parent": 172}, {"command": 5, "file": 68, "line": 9, "parent": 173}, {"file": 67, "parent": 174}, {"command": 4, "file": 67, "line": 56, "parent": 175}, {"command": 6, "file": 70, "line": 21, "parent": 169}, {"file": 79, "parent": 177}, {"command": 5, "file": 79, "line": 41, "parent": 178}, {"file": 78, "parent": 179}, {"command": 6, "file": 78, "line": 21, "parent": 180}, {"file": 77, "parent": 181}, {"command": 5, "file": 77, "line": 41, "parent": 182}, {"file": 76, "parent": 183}, {"command": 5, "file": 76, "line": 9, "parent": 184}, {"file": 75, "parent": 185}, {"command": 4, "file": 75, "line": 56, "parent": 186}, {"command": 8, "file": 1, "line": 5, "parent": 0}, {"command": 9, "file": 0, "line": 166, "parent": 1}, {"command": 9, "file": 2, "line": 141, "parent": 5}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-O2 -g -DNDEBUG"}, {"backtrace": 188, "fragment": "-Wall"}, {"backtrace": 188, "fragment": "-Wextra"}, {"backtrace": 188, "fragment": "-Wpedantic"}, {"backtrace": 188, "fragment": "-Wconversion"}], "defines": [{"backtrace": 4, "define": "BOOST_ALL_NO_LIB"}, {"backtrace": 4, "define": "DEFAULT_RMW_IMPLEMENTATION=rmw_fastrtps_cpp"}, {"backtrace": 4, "define": "FMT_LOCALE"}, {"backtrace": 4, "define": "FMT_SHARED"}, {"backtrace": 4, "define": "KINEMATICS_INTERFACE_BUILDING_DLL"}, {"backtrace": 4, "define": "RCUTILS_ENABLE_FAULT_INJECTION"}], "includes": [{"backtrace": 189, "path": "/opt/ros/humble/src/gmock_vendor/include"}, {"backtrace": 189, "path": "/opt/ros/humble/src/gtest_vendor/include"}, {"backtrace": 4, "path": "/home/<USER>/code/ros2_control/src/ros-controls/ros2_controllers/admittance_controller/include"}, {"backtrace": 4, "path": "/home/<USER>/code/ros2_control/build/admittance_controller/include"}, {"backtrace": 190, "isSystem": true, "path": "/home/<USER>/code/ros2_control/install/include/control_msgs"}, {"backtrace": 190, "isSystem": true, "path": "/home/<USER>/code/ros2_control/install/include/controller_interface"}, {"backtrace": 190, "isSystem": true, "path": "/home/<USER>/code/ros2_control/install/include/hardware_interface"}, {"backtrace": 190, "isSystem": true, "path": "/home/<USER>/code/ros2_control/install/include/ros2_control_test_assets"}, {"backtrace": 190, "isSystem": true, "path": "/home/<USER>/code/ros2_control/install/include"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/parameter_traits"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rclcpp"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/ament_index_cpp"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/libstatistics_collector"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/builtin_interfaces"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_runtime_c"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rcutils"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_interface"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/fastcdr"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_runtime_cpp"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_fastrtps_cpp"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rmw"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_fastrtps_c"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_introspection_c"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_introspection_cpp"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rcl"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rcl_interfaces"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rcl_logging_interface"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rcl_yaml_param_parser"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/libyaml_vendor"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/tracetools"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rcpputils"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/statistics_msgs"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rosgraph_msgs"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_cpp"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_c"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rsl"}, {"backtrace": 4, "isSystem": true, "path": "/usr/include/eigen3"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rclcpp_lifecycle"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rcl_lifecycle"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/lifecycle_msgs"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/angles"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/action_msgs"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/unique_identifier_msgs"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/geometry_msgs"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/std_msgs"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/sensor_msgs"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/trajectory_msgs"}, {"backtrace": 4, "isSystem": true, "path": "/home/<USER>/code/ros2_control/install/include/control_toolbox"}, {"backtrace": 4, "isSystem": true, "path": "/home/<USER>/code/ros2_control/install/include/realtime_tools"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rclcpp_action"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rcl_action"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/filters"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/pluginlib"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/class_loader"}, {"backtrace": 4, "isSystem": true, "path": "/home/<USER>/code/ros2_control/install/include/kinematics_interface"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/tf2"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/tf2_eigen"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/tf2_ros"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/message_filters"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/tf2_msgs"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/tf2_geometry_msgs"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/tf2_kdl"}], "language": "CXX", "sourceIndexes": [0]}], "dependencies": [{"backtrace": 4, "id": "admittance_controller_parameters::@6890427a1f51a3e7e1df"}, {"backtrace": 4, "id": "admittance_controller::@6890427a1f51a3e7e1df"}, {"backtrace": 3, "id": "gmock::@0590522bda6a4df7f28a"}], "id": "test_admittance_controller::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-O2 -g -DNDEBUG", "role": "flags"}, {"fragment": "-Wl,--no-as-needed  /opt/ros/humble/lib/libbackward.so -Wl,--as-needed", "role": "flags"}, {"fragment": "-Wl,-rpath,/home/<USER>/code/ros2_control/build/admittance_controller:/home/<USER>/code/ros2_control/install/lib:/opt/ros/humble/lib", "role": "libraries"}, {"backtrace": 3, "fragment": "gmock/libgmock.a", "role": "libraries"}, {"backtrace": 4, "fragment": "libadmittance_controller.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/home/<USER>/code/ros2_control/install/lib/libcontroller_interface.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/home/<USER>/code/ros2_control/install/lib/libfake_components.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/home/<USER>/code/ros2_control/install/lib/libmock_components.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/home/<USER>/code/ros2_control/install/lib/libhardware_interface.so", "role": "libraries"}, {"backtrace": 8, "fragment": "-pthread", "role": "libraries"}, {"backtrace": 10, "fragment": "/home/<USER>/code/ros2_control/install/lib/libcontrol_toolbox.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/home/<USER>/code/ros2_control/install/lib/libcontrol_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 17, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/home/<USER>/code/ros2_control/install/lib/libcontrol_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 20, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/home/<USER>/code/ros2_control/install/lib/libcontrol_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 23, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/home/<USER>/code/ros2_control/install/lib/libcontrol_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 26, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/home/<USER>/code/ros2_control/install/lib/libcontrol_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 29, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/home/<USER>/code/ros2_control/install/lib/libcontrol_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/home/<USER>/code/ros2_control/install/lib/libcontrol_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/home/<USER>/code/ros2_control/install/lib/libcontrol_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 32, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 35, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 38, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 10, "fragment": "/home/<USER>/code/ros2_control/install/lib/liblow_pass_filter.so", "role": "libraries"}, {"backtrace": 10, "fragment": "/home/<USER>/code/ros2_control/install/lib/librate_limiter.so", "role": "libraries"}, {"backtrace": 10, "fragment": "/home/<USER>/code/ros2_control/install/lib/libexponential_filter.so", "role": "libraries"}, {"backtrace": 45, "fragment": "/opt/ros/humble/lib/libmean.so", "role": "libraries"}, {"backtrace": 45, "fragment": "/opt/ros/humble/lib/libparams.so", "role": "libraries"}, {"backtrace": 45, "fragment": "/opt/ros/humble/lib/libincrement.so", "role": "libraries"}, {"backtrace": 45, "fragment": "/opt/ros/humble/lib/libmedian.so", "role": "libraries"}, {"backtrace": 45, "fragment": "/opt/ros/humble/lib/libtransfer_function.so", "role": "libraries"}, {"backtrace": 47, "fragment": "/opt/ros/humble/lib/librsl.so", "role": "libraries"}, {"backtrace": 10, "fragment": "/home/<USER>/code/ros2_control/install/lib/libkinematics_interface.so", "role": "libraries"}, {"backtrace": 58, "fragment": "/usr/lib/x86_64-linux-gnu/libtinyxml2.so", "role": "libraries"}, {"backtrace": 71, "fragment": "/opt/ros/humble/lib/libclass_loader.so", "role": "libraries"}, {"backtrace": 10, "fragment": "/opt/ros/humble/lib/librclcpp_lifecycle.so", "role": "libraries"}, {"backtrace": 78, "fragment": "/opt/ros/humble/lib/librcl_lifecycle.so", "role": "libraries"}, {"backtrace": 58, "fragment": "/opt/ros/humble/lib/liblifecycle_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 58, "fragment": "/opt/ros/humble/lib/liblifecycle_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 58, "fragment": "/opt/ros/humble/lib/liblifecycle_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 58, "fragment": "/opt/ros/humble/lib/liblifecycle_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 58, "fragment": "/opt/ros/humble/lib/liblifecycle_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 58, "fragment": "/opt/ros/humble/lib/liblifecycle_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 58, "fragment": "/opt/ros/humble/lib/liblifecycle_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 58, "fragment": "/opt/ros/humble/lib/liblifecycle_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 10, "fragment": "/home/<USER>/code/ros2_control/install/lib/librealtime_tools.so", "role": "libraries"}, {"backtrace": 10, "fragment": "/home/<USER>/code/ros2_control/install/lib/libthread_priority.so", "role": "libraries"}, {"backtrace": 85, "fragment": "-lcap", "role": "libraries"}, {"backtrace": 92, "fragment": "/usr/lib/x86_64-linux-gnu/liborocos-kdl.so", "role": "libraries"}, {"backtrace": 10, "fragment": "/opt/ros/humble/lib/libstatic_transform_broadcaster_node.so", "role": "libraries"}, {"backtrace": 10, "fragment": "/opt/ros/humble/lib/libtf2_ros.so", "role": "libraries"}, {"backtrace": 10, "fragment": "/opt/ros/humble/lib/libtf2.so", "role": "libraries"}, {"backtrace": 101, "fragment": "/usr/lib/x86_64-linux-gnu/libconsole_bridge.so.1.0", "role": "libraries"}, {"backtrace": 85, "fragment": "/opt/ros/humble/lib/librclcpp_action.so", "role": "libraries"}, {"backtrace": 110, "fragment": "/opt/ros/humble/lib/librcl_action.so", "role": "libraries"}, {"backtrace": 121, "fragment": "/opt/ros/humble/lib/libmessage_filters.so", "role": "libraries"}, {"backtrace": 10, "fragment": "/opt/ros/humble/lib/librclcpp.so", "role": "libraries"}, {"backtrace": 132, "fragment": "/opt/ros/humble/lib/liblibstatistics_collector.so", "role": "libraries"}, {"backtrace": 132, "fragment": "/opt/ros/humble/lib/librcl.so", "role": "libraries"}, {"backtrace": 145, "fragment": "/opt/ros/humble/lib/librmw_implementation.so", "role": "libraries"}, {"backtrace": 71, "fragment": "/opt/ros/humble/lib/libament_index_cpp.so", "role": "libraries"}, {"backtrace": 145, "fragment": "/opt/ros/humble/lib/librcl_logging_spdlog.so", "role": "libraries"}, {"backtrace": 145, "fragment": "/opt/ros/humble/lib/librcl_logging_interface.so", "role": "libraries"}, {"backtrace": 47, "fragment": "/usr/lib/x86_64-linux-gnu/libfmt.so.8.1.1", "role": "libraries"}, {"backtrace": 150, "fragment": "-Wl,--as-needed", "role": "libraries"}, {"backtrace": 132, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 132, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 132, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 132, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 132, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 132, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 132, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 132, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 132, "fragment": "/opt/ros/humble/lib/librcl_yaml_param_parser.so", "role": "libraries"}, {"backtrace": 159, "fragment": "/opt/ros/humble/lib/libyaml.so", "role": "libraries"}, {"backtrace": 132, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 132, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 132, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 132, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 132, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 132, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 132, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 132, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 132, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 132, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 132, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 132, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 132, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 132, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 132, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 132, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 132, "fragment": "/opt/ros/humble/lib/libtracetools.so", "role": "libraries"}, {"backtrace": 121, "fragment": "/opt/ros/humble/lib/libtf2_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 17, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 17, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 121, "fragment": "/opt/ros/humble/lib/libtf2_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 23, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 23, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 121, "fragment": "/opt/ros/humble/lib/libtf2_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 20, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 20, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 121, "fragment": "/opt/ros/humble/lib/libtf2_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 26, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 26, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 121, "fragment": "/opt/ros/humble/lib/libtf2_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 29, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 29, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 121, "fragment": "/opt/ros/humble/lib/libtf2_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 32, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 32, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 121, "fragment": "/opt/ros/humble/lib/libtf2_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 35, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 35, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 121, "fragment": "/opt/ros/humble/lib/libtf2_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 38, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 38, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 10, "fragment": "/opt/ros/humble/lib/libtrajectory_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 10, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 17, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 17, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 17, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 10, "fragment": "/opt/ros/humble/lib/libtrajectory_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 10, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 20, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 20, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 17, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 17, "fragment": "/opt/ros/humble/lib/libfastcdr.so.1.0.24", "role": "libraries"}, {"backtrace": 20, "fragment": "/opt/ros/humble/lib/librmw.so", "role": "libraries"}, {"backtrace": 10, "fragment": "/opt/ros/humble/lib/libtrajectory_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 10, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 23, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 23, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 10, "fragment": "/opt/ros/humble/lib/libtrajectory_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 10, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 26, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 26, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 26, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 23, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 10, "fragment": "/opt/ros/humble/lib/libtrajectory_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 10, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 29, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 29, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 132, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 10, "fragment": "/opt/ros/humble/lib/libtrajectory_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 10, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 10, "fragment": "/opt/ros/humble/lib/libtrajectory_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 10, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 10, "fragment": "/opt/ros/humble/lib/libtrajectory_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 10, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 32, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 35, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 38, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 32, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 35, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 38, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 176, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 38, "fragment": "/opt/ros/humble/lib/librosidl_runtime_c.so", "role": "libraries"}, {"backtrace": 58, "fragment": "/opt/ros/humble/lib/librcpputils.so", "role": "libraries"}, {"backtrace": 38, "fragment": "/opt/ros/humble/lib/librcutils.so", "role": "libraries"}, {"backtrace": 187, "fragment": "-ldl", "role": "libraries"}, {"backtrace": 32, "fragment": "/usr/lib/x86_64-linux-gnu/libpython3.10.so", "role": "libraries"}], "language": "CXX"}, "name": "test_admittance_controller", "nameOnDisk": "test_admittance_controller", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 2, "compileGroupIndex": 0, "path": "test/test_admittance_controller.cpp", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}