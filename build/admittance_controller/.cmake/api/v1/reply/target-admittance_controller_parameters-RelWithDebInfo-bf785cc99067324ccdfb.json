{"backtrace": 2, "backtraceGraph": {"commands": ["add_library", "generate_parameter_library"], "files": ["/opt/ros/humble/share/generate_parameter_library/cmake/generate_parameter_library.cmake", "CMakeLists.txt"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 36, "parent": 0}, {"command": 0, "file": 0, "line": 86, "parent": 1}]}, "id": "admittance_controller_parameters::@6890427a1f51a3e7e1df", "name": "admittance_controller_parameters", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "<PERSON><PERSON>", "sourceIndexes": [0, 1]}, {"name": "CMake Rules", "sourceIndexes": [2, 3]}], "sources": [{"backtrace": 2, "isGenerated": true, "path": "/home/<USER>/code/ros2_control/build/admittance_controller/include/admittance_controller/admittance_controller_parameters.hpp", "sourceGroupIndex": 0}, {"backtrace": 2, "isGenerated": true, "path": "/home/<USER>/code/ros2_control/build/admittance_controller/include/admittance_controller_parameters.hpp", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "/home/<USER>/code/ros2_control/build/admittance_controller/include/admittance_controller/admittance_controller_parameters.hpp.rule", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "/home/<USER>/code/ros2_control/build/admittance_controller/include/admittance_controller_parameters.hpp.rule", "sourceGroupIndex": 1}], "type": "INTERFACE_LIBRARY"}