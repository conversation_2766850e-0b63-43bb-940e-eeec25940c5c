{"artifacts": [{"path": "libadmittance_controller.so"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "_install", "ament_cmake_symlink_install_targets", "install", "target_link_libraries", "ament_target_dependencies", "set_target_properties", "include", "find_package", "generate_parameter_library", "add_compile_options", "target_compile_definitions", "target_include_directories"], "files": ["CMakeLists.txt", "/opt/ros/humble/share/ament_cmake_core/cmake/symlink_install/ament_cmake_symlink_install_targets.cmake", "/opt/ros/humble/share/ament_cmake_core/cmake/symlink_install/install.cmake", "/opt/ros/humble/share/ament_cmake_target_dependencies/cmake/ament_target_dependencies.cmake", "/home/<USER>/code/ros2_control/install/share/realtime_tools/cmake/export_realtime_toolsExport.cmake", "/home/<USER>/code/ros2_control/install/share/realtime_tools/cmake/ament_cmake_export_targets-extras.cmake", "/home/<USER>/code/ros2_control/install/share/realtime_tools/cmake/realtime_toolsConfig.cmake", "/home/<USER>/code/ros2_control/install/share/control_toolbox/cmake/ament_cmake_export_dependencies-extras.cmake", "/home/<USER>/code/ros2_control/install/share/control_toolbox/cmake/control_toolboxConfig.cmake", "/opt/ros/humble/share/generate_parameter_library/cmake/generate_parameter_library.cmake", "/home/<USER>/code/ros2_control/install/share/control_toolbox/cmake/export_control_toolboxExport.cmake", "/home/<USER>/code/ros2_control/install/share/control_toolbox/cmake/ament_cmake_export_targets-extras.cmake", "/home/<USER>/code/ros2_control/install/share/control_msgs/cmake/export_control_msgs__rosidl_typesupport_fastrtps_cExport.cmake", "/home/<USER>/code/ros2_control/install/share/control_msgs/cmake/ament_cmake_export_targets-extras.cmake", "/home/<USER>/code/ros2_control/install/share/control_msgs/cmake/control_msgsConfig.cmake", "/home/<USER>/code/ros2_control/install/share/control_msgs/cmake/export_control_msgs__rosidl_typesupport_fastrtps_cppExport.cmake", "/home/<USER>/code/ros2_control/install/share/control_msgs/cmake/control_msgs__rosidl_typesupport_introspection_cExport.cmake", "/home/<USER>/code/ros2_control/install/share/control_msgs/cmake/control_msgs__rosidl_typesupport_introspection_cppExport.cmake", "/home/<USER>/code/ros2_control/install/share/control_msgs/cmake/control_msgs__rosidl_typesupport_cppExport.cmake", "/home/<USER>/code/ros2_control/install/share/control_msgs/cmake/export_control_msgs__rosidl_generator_pyExport.cmake", "/home/<USER>/code/ros2_control/install/share/control_msgs/cmake/control_msgs__rosidl_typesupport_cExport.cmake", "/home/<USER>/code/ros2_control/install/share/control_msgs/cmake/export_control_msgs__rosidl_generator_cExport.cmake", "/opt/ros/humble/share/pluginlib/cmake/export_pluginlibExport.cmake", "/opt/ros/humble/share/pluginlib/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/pluginlib/cmake/pluginlibConfig.cmake", "/opt/ros/humble/share/filters/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/filters/cmake/filtersConfig.cmake", "/home/<USER>/code/ros2_control/install/share/hardware_interface/cmake/export_hardware_interfaceExport.cmake", "/home/<USER>/code/ros2_control/install/share/hardware_interface/cmake/ament_cmake_export_targets-extras.cmake", "/home/<USER>/code/ros2_control/install/share/hardware_interface/cmake/hardware_interfaceConfig.cmake", "/home/<USER>/code/ros2_control/install/share/controller_interface/cmake/ament_cmake_export_dependencies-extras.cmake", "/home/<USER>/code/ros2_control/install/share/controller_interface/cmake/controller_interfaceConfig.cmake", "/opt/ros/humble/share/rclcpp_lifecycle/cmake/rclcpp_lifecycleExport.cmake", "/opt/ros/humble/share/rclcpp_lifecycle/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rclcpp_lifecycle/cmake/rclcpp_lifecycleConfig.cmake", "/opt/ros/humble/share/tf2_geometry_msgs/cmake/export_tf2_geometry_msgsExport.cmake", "/opt/ros/humble/share/tf2_geometry_msgs/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/tf2_geometry_msgs/cmake/tf2_geometry_msgsConfig.cmake", "/opt/ros/humble/share/class_loader/cmake/class_loaderExport.cmake", "/opt/ros/humble/share/class_loader/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/class_loader/cmake/class_loaderConfig.cmake", "/opt/ros/humble/share/pluginlib/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/rclcpp_action/cmake/rclcpp_actionExport.cmake", "/opt/ros/humble/share/rclcpp_action/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rclcpp_action/cmake/rclcpp_actionConfig.cmake", "/home/<USER>/code/ros2_control/install/share/realtime_tools/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/tf2_ros/cmake/export_tf2_rosExport.cmake", "/opt/ros/humble/share/tf2_ros/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/tf2_ros/cmake/tf2_rosConfig.cmake", "/opt/ros/humble/share/tf2_eigen/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/tf2_eigen/cmake/tf2_eigenConfig.cmake", "/opt/ros/humble/share/rclcpp/cmake/rclcppExport.cmake", "/opt/ros/humble/share/rclcpp/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rclcpp/cmake/rclcppConfig.cmake", "/opt/ros/humble/share/parameter_traits/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/parameter_traits/cmake/parameter_traitsConfig.cmake", "/opt/ros/humble/share/rcl/cmake/rclExport.cmake", "/opt/ros/humble/share/rcl/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rcl/cmake/rclConfig.cmake", "/opt/ros/humble/share/libstatistics_collector/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/libstatistics_collector/cmake/libstatistics_collectorConfig.cmake", "/opt/ros/humble/share/rclcpp/cmake/ament_cmake_export_dependencies-extras.cmake", "/usr/lib/x86_64-linux-gnu/cmake/fmt/fmt-targets.cmake", "/usr/lib/x86_64-linux-gnu/cmake/fmt/fmt-config.cmake", "/opt/ros/humble/share/rcl_yaml_param_parser/cmake/rcl_yaml_param_parserExport.cmake", "/opt/ros/humble/share/rcl_yaml_param_parser/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rcl_yaml_param_parser/cmake/rcl_yaml_param_parserConfig.cmake", "/opt/ros/humble/share/rcl/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/rosidl_typesupport_cpp/cmake/rosidl_typesupport_cppExport.cmake", "/opt/ros/humble/share/rosidl_typesupport_cpp/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rosidl_typesupport_cpp/cmake/rosidl_typesupport_cppConfig.cmake", "/opt/ros/humble/share/builtin_interfaces/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/builtin_interfaces/cmake/builtin_interfacesConfig.cmake", "/opt/ros/humble/share/action_msgs/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/action_msgs/cmake/action_msgsConfig.cmake", "/home/<USER>/code/ros2_control/install/share/control_msgs/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/rcutils/cmake/rcutilsExport.cmake", "/opt/ros/humble/share/rcutils/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rcutils/cmake/rcutilsConfig.cmake", "/opt/ros/humble/share/rosidl_runtime_c/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/rosidl_runtime_c/cmake/rosidl_runtime_cConfig.cmake"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 40, "parent": 0}, {"command": 3, "file": 0, "line": 98, "parent": 0}, {"command": 2, "file": 2, "line": 37, "parent": 2}, {"command": 1, "file": 1, "line": 50, "parent": 3}, {"command": 5, "file": 0, "line": 52, "parent": 0}, {"command": 4, "file": 3, "line": 145, "parent": 5}, {"command": 8, "file": 0, "line": 32, "parent": 0}, {"file": 8, "parent": 7}, {"command": 7, "file": 8, "line": 41, "parent": 8}, {"file": 7, "parent": 9}, {"command": 8, "file": 7, "line": 21, "parent": 10}, {"file": 6, "parent": 11}, {"command": 7, "file": 6, "line": 41, "parent": 12}, {"file": 5, "parent": 13}, {"command": 7, "file": 5, "line": 9, "parent": 14}, {"file": 4, "parent": 15}, {"command": 6, "file": 4, "line": 61, "parent": 16}, {"command": 9, "file": 0, "line": 36, "parent": 0}, {"command": 4, "file": 9, "line": 92, "parent": 18}, {"command": 7, "file": 8, "line": 41, "parent": 8}, {"file": 11, "parent": 20}, {"command": 7, "file": 11, "line": 9, "parent": 21}, {"file": 10, "parent": 22}, {"command": 6, "file": 10, "line": 70, "parent": 23}, {"command": 8, "file": 0, "line": 32, "parent": 0}, {"file": 14, "parent": 25}, {"command": 7, "file": 14, "line": 41, "parent": 26}, {"file": 13, "parent": 27}, {"command": 7, "file": 13, "line": 9, "parent": 28}, {"file": 12, "parent": 29}, {"command": 6, "file": 12, "line": 61, "parent": 30}, {"command": 7, "file": 13, "line": 9, "parent": 28}, {"file": 15, "parent": 32}, {"command": 6, "file": 15, "line": 61, "parent": 33}, {"command": 7, "file": 13, "line": 9, "parent": 28}, {"file": 16, "parent": 35}, {"command": 6, "file": 16, "line": 61, "parent": 36}, {"command": 7, "file": 13, "line": 9, "parent": 28}, {"file": 17, "parent": 38}, {"command": 6, "file": 17, "line": 61, "parent": 39}, {"command": 7, "file": 13, "line": 9, "parent": 28}, {"file": 18, "parent": 41}, {"command": 6, "file": 18, "line": 61, "parent": 42}, {"command": 7, "file": 13, "line": 9, "parent": 28}, {"file": 19, "parent": 44}, {"command": 6, "file": 19, "line": 61, "parent": 45}, {"command": 7, "file": 13, "line": 9, "parent": 28}, {"file": 20, "parent": 47}, {"command": 6, "file": 20, "line": 61, "parent": 48}, {"command": 7, "file": 13, "line": 9, "parent": 28}, {"file": 21, "parent": 50}, {"command": 6, "file": 21, "line": 61, "parent": 51}, {"command": 8, "file": 7, "line": 21, "parent": 10}, {"file": 26, "parent": 53}, {"command": 7, "file": 26, "line": 41, "parent": 54}, {"file": 25, "parent": 55}, {"command": 8, "file": 25, "line": 21, "parent": 56}, {"file": 24, "parent": 57}, {"command": 7, "file": 24, "line": 41, "parent": 58}, {"file": 23, "parent": 59}, {"command": 7, "file": 23, "line": 9, "parent": 60}, {"file": 22, "parent": 61}, {"command": 6, "file": 22, "line": 56, "parent": 62}, {"command": 8, "file": 0, "line": 32, "parent": 0}, {"file": 31, "parent": 64}, {"command": 7, "file": 31, "line": 41, "parent": 65}, {"file": 30, "parent": 66}, {"command": 8, "file": 30, "line": 21, "parent": 67}, {"file": 29, "parent": 68}, {"command": 7, "file": 29, "line": 41, "parent": 69}, {"file": 28, "parent": 70}, {"command": 7, "file": 28, "line": 9, "parent": 71}, {"file": 27, "parent": 72}, {"command": 6, "file": 27, "line": 61, "parent": 73}, {"command": 8, "file": 7, "line": 21, "parent": 10}, {"file": 34, "parent": 75}, {"command": 7, "file": 34, "line": 41, "parent": 76}, {"file": 33, "parent": 77}, {"command": 7, "file": 33, "line": 9, "parent": 78}, {"file": 32, "parent": 79}, {"command": 6, "file": 32, "line": 56, "parent": 80}, {"command": 8, "file": 0, "line": 32, "parent": 0}, {"file": 37, "parent": 82}, {"command": 7, "file": 37, "line": 41, "parent": 83}, {"file": 36, "parent": 84}, {"command": 7, "file": 36, "line": 9, "parent": 85}, {"file": 35, "parent": 86}, {"command": 6, "file": 35, "line": 56, "parent": 87}, {"command": 7, "file": 24, "line": 41, "parent": 58}, {"file": 41, "parent": 89}, {"command": 8, "file": 41, "line": 21, "parent": 90}, {"file": 40, "parent": 91}, {"command": 7, "file": 40, "line": 41, "parent": 92}, {"file": 39, "parent": 93}, {"command": 7, "file": 39, "line": 9, "parent": 94}, {"file": 38, "parent": 95}, {"command": 6, "file": 38, "line": 56, "parent": 96}, {"command": 7, "file": 6, "line": 41, "parent": 12}, {"file": 45, "parent": 98}, {"command": 8, "file": 45, "line": 21, "parent": 99}, {"file": 44, "parent": 100}, {"command": 7, "file": 44, "line": 41, "parent": 101}, {"file": 43, "parent": 102}, {"command": 7, "file": 43, "line": 9, "parent": 103}, {"file": 42, "parent": 104}, {"command": 6, "file": 42, "line": 56, "parent": 105}, {"command": 8, "file": 0, "line": 32, "parent": 0}, {"file": 50, "parent": 107}, {"command": 7, "file": 50, "line": 41, "parent": 108}, {"file": 49, "parent": 109}, {"command": 8, "file": 49, "line": 21, "parent": 110}, {"file": 48, "parent": 111}, {"command": 7, "file": 48, "line": 41, "parent": 112}, {"file": 47, "parent": 113}, {"command": 7, "file": 47, "line": 9, "parent": 114}, {"file": 46, "parent": 115}, {"command": 6, "file": 46, "line": 56, "parent": 116}, {"command": 8, "file": 7, "line": 21, "parent": 10}, {"file": 55, "parent": 118}, {"command": 7, "file": 55, "line": 41, "parent": 119}, {"file": 54, "parent": 120}, {"command": 8, "file": 54, "line": 21, "parent": 121}, {"file": 53, "parent": 122}, {"command": 7, "file": 53, "line": 41, "parent": 123}, {"file": 52, "parent": 124}, {"command": 7, "file": 52, "line": 9, "parent": 125}, {"file": 51, "parent": 126}, {"command": 6, "file": 51, "line": 56, "parent": 127}, {"command": 7, "file": 53, "line": 41, "parent": 123}, {"file": 61, "parent": 129}, {"command": 8, "file": 61, "line": 21, "parent": 130}, {"file": 60, "parent": 131}, {"command": 7, "file": 60, "line": 41, "parent": 132}, {"file": 59, "parent": 133}, {"command": 8, "file": 59, "line": 21, "parent": 134}, {"file": 58, "parent": 135}, {"command": 7, "file": 58, "line": 41, "parent": 136}, {"file": 57, "parent": 137}, {"command": 7, "file": 57, "line": 9, "parent": 138}, {"file": 56, "parent": 139}, {"command": 6, "file": 56, "line": 56, "parent": 140}, {"command": 8, "file": 7, "line": 21, "parent": 10}, {"file": 63, "parent": 142}, {"command": 7, "file": 63, "line": 37, "parent": 143}, {"file": 62, "parent": 144}, {"command": 6, "file": 62, "line": 66, "parent": 145}, {"command": 7, "file": 58, "line": 41, "parent": 136}, {"file": 67, "parent": 147}, {"command": 8, "file": 67, "line": 21, "parent": 148}, {"file": 66, "parent": 149}, {"command": 7, "file": 66, "line": 41, "parent": 150}, {"file": 65, "parent": 151}, {"command": 7, "file": 65, "line": 9, "parent": 152}, {"file": 64, "parent": 153}, {"command": 6, "file": 64, "line": 56, "parent": 154}, {"command": 7, "file": 14, "line": 41, "parent": 26}, {"file": 75, "parent": 156}, {"command": 8, "file": 75, "line": 21, "parent": 157}, {"file": 74, "parent": 158}, {"command": 7, "file": 74, "line": 41, "parent": 159}, {"file": 73, "parent": 160}, {"command": 8, "file": 73, "line": 21, "parent": 161}, {"file": 72, "parent": 162}, {"command": 7, "file": 72, "line": 41, "parent": 163}, {"file": 71, "parent": 164}, {"command": 8, "file": 71, "line": 21, "parent": 165}, {"file": 70, "parent": 166}, {"command": 7, "file": 70, "line": 41, "parent": 167}, {"file": 69, "parent": 168}, {"command": 7, "file": 69, "line": 9, "parent": 169}, {"file": 68, "parent": 170}, {"command": 6, "file": 68, "line": 56, "parent": 171}, {"command": 8, "file": 71, "line": 21, "parent": 165}, {"file": 80, "parent": 173}, {"command": 7, "file": 80, "line": 41, "parent": 174}, {"file": 79, "parent": 175}, {"command": 8, "file": 79, "line": 21, "parent": 176}, {"file": 78, "parent": 177}, {"command": 7, "file": 78, "line": 41, "parent": 178}, {"file": 77, "parent": 179}, {"command": 7, "file": 77, "line": 9, "parent": 180}, {"file": 76, "parent": 181}, {"command": 6, "file": 76, "line": 56, "parent": 182}, {"command": 4, "file": 0, "line": 48, "parent": 0}, {"command": 10, "file": 0, "line": 5, "parent": 0}, {"command": 11, "file": 0, "line": 56, "parent": 0}, {"command": 12, "file": 0, "line": 44, "parent": 0}, {"command": 12, "file": 3, "line": 141, "parent": 5}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-O2 -g -DNDEBUG -fPIC"}, {"backtrace": 185, "fragment": "-Wall"}, {"backtrace": 185, "fragment": "-Wextra"}, {"backtrace": 185, "fragment": "-Wpedantic"}, {"backtrace": 185, "fragment": "-Wconversion"}], "defines": [{"backtrace": 186, "define": "ADMITTANCE_CONTROLLER_BUILDING_DLL"}, {"backtrace": 6, "define": "BOOST_ALL_NO_LIB"}, {"backtrace": 184, "define": "DEFAULT_RMW_IMPLEMENTATION=rmw_fastrtps_cpp"}, {"backtrace": 184, "define": "FMT_LOCALE"}, {"backtrace": 184, "define": "FMT_SHARED"}, {"backtrace": 6, "define": "KINEMATICS_INTERFACE_BUILDING_DLL"}, {"backtrace": 184, "define": "RCUTILS_ENABLE_FAULT_INJECTION"}, {"define": "admittance_controller_EXPORTS"}], "includes": [{"backtrace": 187, "path": "/home/<USER>/code/ros2_control/src/ros-controls/ros2_controllers/admittance_controller/include"}, {"backtrace": 184, "path": "/home/<USER>/code/ros2_control/build/admittance_controller/include"}, {"backtrace": 188, "isSystem": true, "path": "/home/<USER>/code/ros2_control/install/include/control_msgs"}, {"backtrace": 188, "isSystem": true, "path": "/home/<USER>/code/ros2_control/install/include/control_toolbox"}, {"backtrace": 188, "isSystem": true, "path": "/home/<USER>/code/ros2_control/install/include"}, {"backtrace": 188, "isSystem": true, "path": "/home/<USER>/code/ros2_control/install/include/controller_interface"}, {"backtrace": 188, "isSystem": true, "path": "/home/<USER>/code/ros2_control/install/include/hardware_interface"}, {"backtrace": 188, "isSystem": true, "path": "/home/<USER>/code/ros2_control/install/include/kinematics_interface"}, {"backtrace": 188, "isSystem": true, "path": "/home/<USER>/code/ros2_control/install/include/realtime_tools"}, {"backtrace": 188, "isSystem": true, "path": "/opt/ros/humble/include/angles"}, {"backtrace": 188, "isSystem": true, "path": "/opt/ros/humble/include/geometry_msgs"}, {"backtrace": 188, "isSystem": true, "path": "/opt/ros/humble/include/pluginlib"}, {"backtrace": 188, "isSystem": true, "path": "/opt/ros/humble/include/rclcpp"}, {"backtrace": 188, "isSystem": true, "path": "/opt/ros/humble/include/rclcpp_lifecycle"}, {"backtrace": 188, "isSystem": true, "path": "/opt/ros/humble/include/tf2"}, {"backtrace": 188, "isSystem": true, "path": "/opt/ros/humble/include/tf2_eigen"}, {"backtrace": 188, "isSystem": true, "path": "/opt/ros/humble/include/tf2_geometry_msgs"}, {"backtrace": 188, "isSystem": true, "path": "/opt/ros/humble/include/tf2_kdl"}, {"backtrace": 188, "isSystem": true, "path": "/opt/ros/humble/include/tf2_ros"}, {"backtrace": 188, "isSystem": true, "path": "/opt/ros/humble/include/trajectory_msgs"}, {"backtrace": 184, "isSystem": true, "path": "/opt/ros/humble/include/parameter_traits"}, {"backtrace": 184, "isSystem": true, "path": "/opt/ros/humble/include/ament_index_cpp"}, {"backtrace": 184, "isSystem": true, "path": "/opt/ros/humble/include/libstatistics_collector"}, {"backtrace": 184, "isSystem": true, "path": "/opt/ros/humble/include/builtin_interfaces"}, {"backtrace": 184, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_runtime_c"}, {"backtrace": 184, "isSystem": true, "path": "/opt/ros/humble/include/rcutils"}, {"backtrace": 184, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_interface"}, {"backtrace": 184, "isSystem": true, "path": "/opt/ros/humble/include/fastcdr"}, {"backtrace": 184, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_runtime_cpp"}, {"backtrace": 184, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_fastrtps_cpp"}, {"backtrace": 184, "isSystem": true, "path": "/opt/ros/humble/include/rmw"}, {"backtrace": 184, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_fastrtps_c"}, {"backtrace": 184, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_introspection_c"}, {"backtrace": 184, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_introspection_cpp"}, {"backtrace": 184, "isSystem": true, "path": "/opt/ros/humble/include/rcl"}, {"backtrace": 184, "isSystem": true, "path": "/opt/ros/humble/include/rcl_interfaces"}, {"backtrace": 184, "isSystem": true, "path": "/opt/ros/humble/include/rcl_logging_interface"}, {"backtrace": 184, "isSystem": true, "path": "/opt/ros/humble/include/rcl_yaml_param_parser"}, {"backtrace": 184, "isSystem": true, "path": "/opt/ros/humble/include/libyaml_vendor"}, {"backtrace": 184, "isSystem": true, "path": "/opt/ros/humble/include/tracetools"}, {"backtrace": 184, "isSystem": true, "path": "/opt/ros/humble/include/rcpputils"}, {"backtrace": 184, "isSystem": true, "path": "/opt/ros/humble/include/statistics_msgs"}, {"backtrace": 184, "isSystem": true, "path": "/opt/ros/humble/include/rosgraph_msgs"}, {"backtrace": 184, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_cpp"}, {"backtrace": 184, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_c"}, {"backtrace": 184, "isSystem": true, "path": "/opt/ros/humble/include/rsl"}, {"backtrace": 184, "isSystem": true, "path": "/usr/include/eigen3"}, {"backtrace": 184, "isSystem": true, "path": "/opt/ros/humble/include"}, {"backtrace": 184, "isSystem": true, "path": "/opt/ros/humble/include/rcl_lifecycle"}, {"backtrace": 184, "isSystem": true, "path": "/opt/ros/humble/include/lifecycle_msgs"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/humble/include/action_msgs"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/humble/include/unique_identifier_msgs"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/humble/include/std_msgs"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/humble/include/sensor_msgs"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/humble/include/rclcpp_action"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/humble/include/rcl_action"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/humble/include/filters"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/humble/include/class_loader"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/humble/include/message_filters"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/humble/include/tf2_msgs"}], "language": "CXX", "sourceIndexes": [0]}], "dependencies": [{"backtrace": 184, "id": "admittance_controller_parameters::@6890427a1f51a3e7e1df"}], "id": "admittance_controller::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 4, "path": "lib"}, {"backtrace": 4, "path": "lib"}], "prefix": {"path": "/home/<USER>/code/ros2_control/install"}}, "link": {"commandFragments": [{"fragment": "-shared", "role": "flags"}, {"fragment": "-Wl,-rpath,/home/<USER>/code/ros2_control/install/lib:/opt/ros/humble/lib:", "role": "libraries"}, {"backtrace": 6, "fragment": "/home/<USER>/code/ros2_control/install/lib/libcontrol_toolbox.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/home/<USER>/code/ros2_control/install/lib/liblow_pass_filter.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/home/<USER>/code/ros2_control/install/lib/librate_limiter.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/home/<USER>/code/ros2_control/install/lib/libexponential_filter.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/home/<USER>/code/ros2_control/install/lib/libcontroller_interface.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/home/<USER>/code/ros2_control/install/lib/libfake_components.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/home/<USER>/code/ros2_control/install/lib/libmock_components.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/home/<USER>/code/ros2_control/install/lib/libhardware_interface.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/home/<USER>/code/ros2_control/install/lib/libkinematics_interface.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/humble/lib/librclcpp_lifecycle.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/home/<USER>/code/ros2_control/install/lib/librealtime_tools.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/home/<USER>/code/ros2_control/install/lib/libthread_priority.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/humble/lib/libstatic_transform_broadcaster_node.so", "role": "libraries"}, {"backtrace": 17, "fragment": "-lcap", "role": "libraries"}, {"backtrace": 19, "fragment": "/opt/ros/humble/lib/librsl.so", "role": "libraries"}, {"backtrace": 24, "fragment": "/opt/ros/humble/lib/libmean.so", "role": "libraries"}, {"backtrace": 24, "fragment": "/opt/ros/humble/lib/libparams.so", "role": "libraries"}, {"backtrace": 24, "fragment": "/opt/ros/humble/lib/libincrement.so", "role": "libraries"}, {"backtrace": 24, "fragment": "/opt/ros/humble/lib/libmedian.so", "role": "libraries"}, {"backtrace": 24, "fragment": "/opt/ros/humble/lib/libtransfer_function.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/home/<USER>/code/ros2_control/install/lib/libcontrol_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/humble/lib/libtrajectory_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 31, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/home/<USER>/code/ros2_control/install/lib/libcontrol_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/humble/lib/libtrajectory_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 34, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/home/<USER>/code/ros2_control/install/lib/libcontrol_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/humble/lib/libtrajectory_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 37, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/home/<USER>/code/ros2_control/install/lib/libcontrol_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/humble/lib/libtrajectory_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 40, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/home/<USER>/code/ros2_control/install/lib/libcontrol_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/humble/lib/libtrajectory_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 43, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/home/<USER>/code/ros2_control/install/lib/libcontrol_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/home/<USER>/code/ros2_control/install/lib/libcontrol_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/home/<USER>/code/ros2_control/install/lib/libcontrol_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/humble/lib/libtrajectory_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/humble/lib/libtrajectory_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/humble/lib/libtrajectory_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 46, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 49, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 52, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 63, "fragment": "/opt/ros/humble/lib/libclass_loader.so", "role": "libraries"}, {"backtrace": 74, "fragment": "/usr/lib/x86_64-linux-gnu/libtinyxml2.so", "role": "libraries"}, {"backtrace": 81, "fragment": "/opt/ros/humble/lib/librcl_lifecycle.so", "role": "libraries"}, {"backtrace": 74, "fragment": "/opt/ros/humble/lib/liblifecycle_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 74, "fragment": "/opt/ros/humble/lib/liblifecycle_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 74, "fragment": "/opt/ros/humble/lib/liblifecycle_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 74, "fragment": "/opt/ros/humble/lib/liblifecycle_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 74, "fragment": "/opt/ros/humble/lib/liblifecycle_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 74, "fragment": "/opt/ros/humble/lib/liblifecycle_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 74, "fragment": "/opt/ros/humble/lib/liblifecycle_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 74, "fragment": "/opt/ros/humble/lib/liblifecycle_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 88, "fragment": "/usr/lib/x86_64-linux-gnu/liborocos-kdl.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/humble/lib/libtf2_ros.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/humble/lib/libtf2.so", "role": "libraries"}, {"backtrace": 97, "fragment": "/usr/lib/x86_64-linux-gnu/libconsole_bridge.so.1.0", "role": "libraries"}, {"backtrace": 17, "fragment": "/opt/ros/humble/lib/librclcpp_action.so", "role": "libraries"}, {"backtrace": 106, "fragment": "/opt/ros/humble/lib/librcl_action.so", "role": "libraries"}, {"backtrace": 117, "fragment": "/opt/ros/humble/lib/libmessage_filters.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/humble/lib/librclcpp.so", "role": "libraries"}, {"backtrace": 128, "fragment": "/opt/ros/humble/lib/liblibstatistics_collector.so", "role": "libraries"}, {"backtrace": 128, "fragment": "/opt/ros/humble/lib/librcl.so", "role": "libraries"}, {"backtrace": 141, "fragment": "/opt/ros/humble/lib/librmw_implementation.so", "role": "libraries"}, {"backtrace": 63, "fragment": "/opt/ros/humble/lib/libament_index_cpp.so", "role": "libraries"}, {"backtrace": 141, "fragment": "/opt/ros/humble/lib/librcl_logging_spdlog.so", "role": "libraries"}, {"backtrace": 141, "fragment": "/opt/ros/humble/lib/librcl_logging_interface.so", "role": "libraries"}, {"backtrace": 19, "fragment": "/usr/lib/x86_64-linux-gnu/libfmt.so.8.1.1", "role": "libraries"}, {"backtrace": 146, "fragment": "-Wl,--as-needed", "role": "libraries"}, {"backtrace": 128, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 128, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 128, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 128, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 128, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 128, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 128, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 128, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 128, "fragment": "/opt/ros/humble/lib/librcl_yaml_param_parser.so", "role": "libraries"}, {"backtrace": 155, "fragment": "/opt/ros/humble/lib/libyaml.so", "role": "libraries"}, {"backtrace": 128, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 128, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 128, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 128, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 128, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 128, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 128, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 128, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 128, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 128, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 128, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 128, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 128, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 128, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 128, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 128, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 128, "fragment": "/opt/ros/humble/lib/libtracetools.so", "role": "libraries"}, {"backtrace": 117, "fragment": "/opt/ros/humble/lib/libtf2_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 31, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 31, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 31, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 31, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 31, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 117, "fragment": "/opt/ros/humble/lib/libtf2_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 37, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 37, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 37, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 37, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 117, "fragment": "/opt/ros/humble/lib/libtf2_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 34, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 34, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 34, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 34, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 31, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 31, "fragment": "/opt/ros/humble/lib/libfastcdr.so.1.0.24", "role": "libraries"}, {"backtrace": 34, "fragment": "/opt/ros/humble/lib/librmw.so", "role": "libraries"}, {"backtrace": 117, "fragment": "/opt/ros/humble/lib/libtf2_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 40, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 40, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 40, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 40, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 40, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 37, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 117, "fragment": "/opt/ros/humble/lib/libtf2_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 43, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 43, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 43, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 43, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 128, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 117, "fragment": "/opt/ros/humble/lib/libtf2_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 46, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 46, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 46, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 46, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 46, "fragment": "/usr/lib/x86_64-linux-gnu/libpython3.10.so", "role": "libraries"}, {"backtrace": 117, "fragment": "/opt/ros/humble/lib/libtf2_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 49, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 49, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 49, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 49, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 117, "fragment": "/opt/ros/humble/lib/libtf2_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 52, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 52, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 52, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 52, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 172, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 52, "fragment": "/opt/ros/humble/lib/librosidl_runtime_c.so", "role": "libraries"}, {"backtrace": 74, "fragment": "/opt/ros/humble/lib/librcpputils.so", "role": "libraries"}, {"backtrace": 52, "fragment": "/opt/ros/humble/lib/librcutils.so", "role": "libraries"}, {"backtrace": 183, "fragment": "-ldl", "role": "libraries"}], "language": "CXX"}, "name": "admittance_controller", "nameOnDisk": "libadmittance_controller.so", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "src/admittance_controller.cpp", "sourceGroupIndex": 0}], "type": "SHARED_LIBRARY"}