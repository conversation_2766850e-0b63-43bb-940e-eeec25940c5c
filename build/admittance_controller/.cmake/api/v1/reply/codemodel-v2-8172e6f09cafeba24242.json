{"configurations": [{"directories": [{"build": ".", "childIndexes": [1, 2], "hasInstallRule": true, "jsonFile": "directory-.-RelWithDebInfo-ca4ae4c67f4006ea23a5.json", "minimumCMakeVersion": {"string": "3.12"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2, 7, 8, 9]}, {"build": "gmock", "jsonFile": "directory-gmock-RelWithDebInfo-3370d118e31866b3418b.json", "minimumCMakeVersion": {"string": "3.8"}, "parentIndex": 0, "projectIndex": 1, "source": "/opt/ros/humble/src/gmock_vendor", "targetIndexes": [3, 4]}, {"build": "gtest", "jsonFile": "directory-gtest-RelWithDebInfo-a2408fe1a73b92c5e636.json", "minimumCMakeVersion": {"string": "3.8"}, "parentIndex": 0, "projectIndex": 2, "source": "/opt/ros/humble/src/gtest_vendor", "targetIndexes": [5, 6]}], "name": "RelWithDebInfo", "projects": [{"childIndexes": [1, 2], "directoryIndexes": [0], "name": "admittance_controller", "targetIndexes": [0, 1, 2, 7, 8, 9]}, {"directoryIndexes": [1], "name": "gmock", "parentIndex": 0, "targetIndexes": [3, 4]}, {"directoryIndexes": [2], "name": "gtest", "parentIndex": 0, "targetIndexes": [5, 6]}], "targets": [{"directoryIndex": 0, "id": "admittance_controller::@6890427a1f51a3e7e1df", "jsonFile": "target-admittance_controller-RelWithDebInfo-a23ebe542a576212158b.json", "name": "admittance_controller", "projectIndex": 0}, {"directoryIndex": 0, "id": "admittance_controller_parameters::@6890427a1f51a3e7e1df", "jsonFile": "target-admittance_controller_parameters-RelWithDebInfo-bf785cc99067324ccdfb.json", "name": "admittance_controller_parameters", "projectIndex": 0}, {"directoryIndex": 0, "id": "admittance_controller_uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-admittance_controller_uninstall-RelWithDebInfo-c3c058fbe53c1740adbc.json", "name": "admittance_controller_uninstall", "projectIndex": 0}, {"directoryIndex": 1, "id": "gmock::@0590522bda6a4df7f28a", "jsonFile": "target-gmock-RelWithDebInfo-2e6c1cc6d8aad7bfdaeb.json", "name": "gmock", "projectIndex": 1}, {"directoryIndex": 1, "id": "gmock_main::@0590522bda6a4df7f28a", "jsonFile": "target-gmock_main-RelWithDebInfo-4dac0666d00ab195ffba.json", "name": "gmock_main", "projectIndex": 1}, {"directoryIndex": 2, "id": "gtest::@0af4add9fe94340e763e", "jsonFile": "target-gtest-RelWithDebInfo-808effc5d6b88b96e2c1.json", "name": "gtest", "projectIndex": 2}, {"directoryIndex": 2, "id": "gtest_main::@0af4add9fe94340e763e", "jsonFile": "target-gtest_main-RelWithDebInfo-b053f9fa70673e569e5b.json", "name": "gtest_main", "projectIndex": 2}, {"directoryIndex": 0, "id": "test_admittance_controller::@6890427a1f51a3e7e1df", "jsonFile": "target-test_admittance_controller-RelWithDebInfo-f4cc200b410d1ab8cf77.json", "name": "test_admittance_controller", "projectIndex": 0}, {"directoryIndex": 0, "id": "test_load_admittance_controller::@6890427a1f51a3e7e1df", "jsonFile": "target-test_load_admittance_controller-RelWithDebInfo-045f91b84d2215a2251b.json", "name": "test_load_admittance_controller", "projectIndex": 0}, {"directoryIndex": 0, "id": "uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-uninstall-RelWithDebInfo-18008f343a1e07c39501.json", "name": "uninstall", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/home/<USER>/code/ros2_control/build/admittance_controller", "source": "/home/<USER>/code/ros2_control/src/ros-controls/ros2_controllers/admittance_controller"}, "version": {"major": 2, "minor": 8}}