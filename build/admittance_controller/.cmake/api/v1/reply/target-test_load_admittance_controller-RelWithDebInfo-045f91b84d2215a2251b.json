{"artifacts": [{"path": "test_load_admittance_controller"}], "backtrace": 2, "backtraceGraph": {"commands": ["add_executable", "add_rostest_with_parameters_gmock", "target_link_libraries", "ament_target_dependencies", "set_target_properties", "include", "find_package", "add_compile_options", "target_include_directories"], "files": ["/opt/ros/humble/share/generate_parameter_library/cmake/generate_parameter_library.cmake", "CMakeLists.txt", "/opt/ros/humble/share/ament_cmake_target_dependencies/cmake/ament_target_dependencies.cmake", "/opt/ros/humble/src/gmock_vendor/CMakeLists.txt", "/home/<USER>/code/ros2_control/install/share/controller_manager/cmake/export_controller_managerExport.cmake", "/home/<USER>/code/ros2_control/install/share/controller_manager/cmake/ament_cmake_export_targets-extras.cmake", "/home/<USER>/code/ros2_control/install/share/controller_manager/cmake/controller_managerConfig.cmake", "/home/<USER>/code/ros2_control/install/share/hardware_interface/cmake/export_hardware_interfaceExport.cmake", "/home/<USER>/code/ros2_control/install/share/hardware_interface/cmake/ament_cmake_export_targets-extras.cmake", "/home/<USER>/code/ros2_control/install/share/hardware_interface/cmake/hardware_interfaceConfig.cmake", "/home/<USER>/code/ros2_control/install/share/controller_interface/cmake/ament_cmake_export_dependencies-extras.cmake", "/home/<USER>/code/ros2_control/install/share/controller_interface/cmake/controller_interfaceConfig.cmake", "/home/<USER>/code/ros2_control/install/share/control_msgs/cmake/export_control_msgs__rosidl_typesupport_fastrtps_cExport.cmake", "/home/<USER>/code/ros2_control/install/share/control_msgs/cmake/ament_cmake_export_targets-extras.cmake", "/home/<USER>/code/ros2_control/install/share/control_msgs/cmake/control_msgsConfig.cmake", "/home/<USER>/code/ros2_control/install/share/control_msgs/cmake/export_control_msgs__rosidl_typesupport_fastrtps_cppExport.cmake", "/home/<USER>/code/ros2_control/install/share/control_msgs/cmake/control_msgs__rosidl_typesupport_introspection_cExport.cmake", "/home/<USER>/code/ros2_control/install/share/control_msgs/cmake/control_msgs__rosidl_typesupport_introspection_cppExport.cmake", "/home/<USER>/code/ros2_control/install/share/control_msgs/cmake/control_msgs__rosidl_typesupport_cppExport.cmake", "/home/<USER>/code/ros2_control/install/share/control_msgs/cmake/export_control_msgs__rosidl_generator_pyExport.cmake", "/home/<USER>/code/ros2_control/install/share/control_msgs/cmake/control_msgs__rosidl_typesupport_cExport.cmake", "/home/<USER>/code/ros2_control/install/share/control_msgs/cmake/export_control_msgs__rosidl_generator_cExport.cmake", "/opt/ros/humble/share/rclcpp_lifecycle/cmake/rclcpp_lifecycleExport.cmake", "/opt/ros/humble/share/rclcpp_lifecycle/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rclcpp_lifecycle/cmake/rclcpp_lifecycleConfig.cmake", "/home/<USER>/code/ros2_control/install/share/control_toolbox/cmake/ament_cmake_export_dependencies-extras.cmake", "/home/<USER>/code/ros2_control/install/share/control_toolbox/cmake/control_toolboxConfig.cmake", "/opt/ros/humble/share/pluginlib/cmake/export_pluginlibExport.cmake", "/opt/ros/humble/share/pluginlib/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/pluginlib/cmake/pluginlibConfig.cmake", "/opt/ros/humble/share/filters/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/filters/cmake/filtersConfig.cmake", "/opt/ros/humble/share/class_loader/cmake/class_loaderExport.cmake", "/opt/ros/humble/share/class_loader/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/class_loader/cmake/class_loaderConfig.cmake", "/opt/ros/humble/share/pluginlib/cmake/ament_cmake_export_dependencies-extras.cmake", "/home/<USER>/code/ros2_control/install/share/realtime_tools/cmake/export_realtime_toolsExport.cmake", "/home/<USER>/code/ros2_control/install/share/realtime_tools/cmake/ament_cmake_export_targets-extras.cmake", "/home/<USER>/code/ros2_control/install/share/realtime_tools/cmake/realtime_toolsConfig.cmake", "/opt/ros/humble/share/rclcpp/cmake/rclcppExport.cmake", "/opt/ros/humble/share/rclcpp/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rclcpp/cmake/rclcppConfig.cmake", "/opt/ros/humble/share/parameter_traits/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/parameter_traits/cmake/parameter_traitsConfig.cmake", "/opt/ros/humble/share/rclcpp_action/cmake/rclcpp_actionExport.cmake", "/opt/ros/humble/share/rclcpp_action/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rclcpp_action/cmake/rclcpp_actionConfig.cmake", "/home/<USER>/code/ros2_control/install/share/realtime_tools/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/rcl_yaml_param_parser/cmake/rcl_yaml_param_parserExport.cmake", "/opt/ros/humble/share/rcl_yaml_param_parser/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rcl_yaml_param_parser/cmake/rcl_yaml_param_parserConfig.cmake", "/opt/ros/humble/share/rcl/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/rcl/cmake/rclConfig.cmake", "/opt/ros/humble/share/libstatistics_collector/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/libstatistics_collector/cmake/libstatistics_collectorConfig.cmake", "/opt/ros/humble/share/rclcpp/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/rcl/cmake/rclExport.cmake", "/opt/ros/humble/share/rcl/cmake/ament_cmake_export_targets-extras.cmake", "/home/<USER>/code/ros2_control/install/share/controller_manager_msgs/cmake/export_controller_manager_msgs__rosidl_typesupport_fastrtps_cExport.cmake", "/home/<USER>/code/ros2_control/install/share/controller_manager_msgs/cmake/ament_cmake_export_targets-extras.cmake", "/home/<USER>/code/ros2_control/install/share/controller_manager_msgs/cmake/controller_manager_msgsConfig.cmake", "/home/<USER>/code/ros2_control/install/share/controller_manager/cmake/ament_cmake_export_dependencies-extras.cmake", "/home/<USER>/code/ros2_control/install/share/controller_manager_msgs/cmake/export_controller_manager_msgs__rosidl_typesupport_fastrtps_cppExport.cmake", "/home/<USER>/code/ros2_control/install/share/controller_manager_msgs/cmake/controller_manager_msgs__rosidl_typesupport_introspection_cExport.cmake", "/home/<USER>/code/ros2_control/install/share/controller_manager_msgs/cmake/controller_manager_msgs__rosidl_typesupport_introspection_cppExport.cmake", "/home/<USER>/code/ros2_control/install/share/controller_manager_msgs/cmake/controller_manager_msgs__rosidl_typesupport_cppExport.cmake", "/home/<USER>/code/ros2_control/install/share/controller_manager_msgs/cmake/export_controller_manager_msgs__rosidl_generator_pyExport.cmake", "/home/<USER>/code/ros2_control/install/share/controller_manager_msgs/cmake/controller_manager_msgs__rosidl_typesupport_cExport.cmake", "/home/<USER>/code/ros2_control/install/share/controller_manager_msgs/cmake/export_controller_manager_msgs__rosidl_generator_cExport.cmake", "/opt/ros/humble/share/rosidl_typesupport_cpp/cmake/rosidl_typesupport_cppExport.cmake", "/opt/ros/humble/share/rosidl_typesupport_cpp/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rosidl_typesupport_cpp/cmake/rosidl_typesupport_cppConfig.cmake", "/opt/ros/humble/share/builtin_interfaces/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/builtin_interfaces/cmake/builtin_interfacesConfig.cmake", "/opt/ros/humble/share/action_msgs/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/action_msgs/cmake/action_msgsConfig.cmake", "/home/<USER>/code/ros2_control/install/share/control_msgs/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/rcutils/cmake/rcutilsExport.cmake", "/opt/ros/humble/share/rcutils/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rcutils/cmake/rcutilsConfig.cmake", "/opt/ros/humble/share/rosidl_runtime_c/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/rosidl_runtime_c/cmake/rosidl_runtime_cConfig.cmake"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 69, "parent": 0}, {"command": 0, "file": 0, "line": 164, "parent": 1}, {"command": 2, "file": 0, "line": 167, "parent": 1}, {"command": 3, "file": 1, "line": 73, "parent": 0}, {"command": 2, "file": 2, "line": 145, "parent": 4}, {"file": 3}, {"command": 2, "file": 3, "line": 25, "parent": 6}, {"command": 6, "file": 1, "line": 62, "parent": 0}, {"file": 6, "parent": 8}, {"command": 5, "file": 6, "line": 41, "parent": 9}, {"file": 5, "parent": 10}, {"command": 5, "file": 5, "line": 9, "parent": 11}, {"file": 4, "parent": 12}, {"command": 4, "file": 4, "line": 61, "parent": 13}, {"command": 6, "file": 1, "line": 32, "parent": 0}, {"file": 11, "parent": 15}, {"command": 5, "file": 11, "line": 41, "parent": 16}, {"file": 10, "parent": 17}, {"command": 6, "file": 10, "line": 21, "parent": 18}, {"file": 9, "parent": 19}, {"command": 5, "file": 9, "line": 41, "parent": 20}, {"file": 8, "parent": 21}, {"command": 5, "file": 8, "line": 9, "parent": 22}, {"file": 7, "parent": 23}, {"command": 4, "file": 7, "line": 61, "parent": 24}, {"command": 6, "file": 1, "line": 32, "parent": 0}, {"file": 14, "parent": 26}, {"command": 5, "file": 14, "line": 41, "parent": 27}, {"file": 13, "parent": 28}, {"command": 5, "file": 13, "line": 9, "parent": 29}, {"file": 12, "parent": 30}, {"command": 4, "file": 12, "line": 61, "parent": 31}, {"command": 5, "file": 13, "line": 9, "parent": 29}, {"file": 15, "parent": 33}, {"command": 4, "file": 15, "line": 61, "parent": 34}, {"command": 5, "file": 13, "line": 9, "parent": 29}, {"file": 16, "parent": 36}, {"command": 4, "file": 16, "line": 61, "parent": 37}, {"command": 5, "file": 13, "line": 9, "parent": 29}, {"file": 17, "parent": 39}, {"command": 4, "file": 17, "line": 61, "parent": 40}, {"command": 5, "file": 13, "line": 9, "parent": 29}, {"file": 18, "parent": 42}, {"command": 4, "file": 18, "line": 61, "parent": 43}, {"command": 5, "file": 13, "line": 9, "parent": 29}, {"file": 19, "parent": 45}, {"command": 4, "file": 19, "line": 61, "parent": 46}, {"command": 5, "file": 13, "line": 9, "parent": 29}, {"file": 20, "parent": 48}, {"command": 4, "file": 20, "line": 61, "parent": 49}, {"command": 5, "file": 13, "line": 9, "parent": 29}, {"file": 21, "parent": 51}, {"command": 4, "file": 21, "line": 61, "parent": 52}, {"command": 6, "file": 1, "line": 32, "parent": 0}, {"file": 26, "parent": 54}, {"command": 5, "file": 26, "line": 41, "parent": 55}, {"file": 25, "parent": 56}, {"command": 6, "file": 25, "line": 21, "parent": 57}, {"file": 24, "parent": 58}, {"command": 5, "file": 24, "line": 41, "parent": 59}, {"file": 23, "parent": 60}, {"command": 5, "file": 23, "line": 9, "parent": 61}, {"file": 22, "parent": 62}, {"command": 4, "file": 22, "line": 56, "parent": 63}, {"command": 6, "file": 25, "line": 21, "parent": 57}, {"file": 31, "parent": 65}, {"command": 5, "file": 31, "line": 41, "parent": 66}, {"file": 30, "parent": 67}, {"command": 6, "file": 30, "line": 21, "parent": 68}, {"file": 29, "parent": 69}, {"command": 5, "file": 29, "line": 41, "parent": 70}, {"file": 28, "parent": 71}, {"command": 5, "file": 28, "line": 9, "parent": 72}, {"file": 27, "parent": 73}, {"command": 4, "file": 27, "line": 56, "parent": 74}, {"command": 5, "file": 29, "line": 41, "parent": 70}, {"file": 35, "parent": 76}, {"command": 6, "file": 35, "line": 21, "parent": 77}, {"file": 34, "parent": 78}, {"command": 5, "file": 34, "line": 41, "parent": 79}, {"file": 33, "parent": 80}, {"command": 5, "file": 33, "line": 9, "parent": 81}, {"file": 32, "parent": 82}, {"command": 4, "file": 32, "line": 56, "parent": 83}, {"command": 6, "file": 25, "line": 21, "parent": 57}, {"file": 38, "parent": 85}, {"command": 5, "file": 38, "line": 41, "parent": 86}, {"file": 37, "parent": 87}, {"command": 5, "file": 37, "line": 9, "parent": 88}, {"file": 36, "parent": 89}, {"command": 4, "file": 36, "line": 61, "parent": 90}, {"command": 6, "file": 25, "line": 21, "parent": 57}, {"file": 43, "parent": 92}, {"command": 5, "file": 43, "line": 41, "parent": 93}, {"file": 42, "parent": 94}, {"command": 6, "file": 42, "line": 21, "parent": 95}, {"file": 41, "parent": 96}, {"command": 5, "file": 41, "line": 41, "parent": 97}, {"file": 40, "parent": 98}, {"command": 5, "file": 40, "line": 9, "parent": 99}, {"file": 39, "parent": 100}, {"command": 4, "file": 39, "line": 56, "parent": 101}, {"command": 5, "file": 38, "line": 41, "parent": 86}, {"file": 47, "parent": 103}, {"command": 6, "file": 47, "line": 21, "parent": 104}, {"file": 46, "parent": 105}, {"command": 5, "file": 46, "line": 41, "parent": 106}, {"file": 45, "parent": 107}, {"command": 5, "file": 45, "line": 9, "parent": 108}, {"file": 44, "parent": 109}, {"command": 4, "file": 44, "line": 56, "parent": 110}, {"command": 5, "file": 41, "line": 41, "parent": 97}, {"file": 55, "parent": 112}, {"command": 6, "file": 55, "line": 21, "parent": 113}, {"file": 54, "parent": 114}, {"command": 5, "file": 54, "line": 41, "parent": 115}, {"file": 53, "parent": 116}, {"command": 6, "file": 53, "line": 21, "parent": 117}, {"file": 52, "parent": 118}, {"command": 5, "file": 52, "line": 41, "parent": 119}, {"file": 51, "parent": 120}, {"command": 6, "file": 51, "line": 21, "parent": 121}, {"file": 50, "parent": 122}, {"command": 5, "file": 50, "line": 41, "parent": 123}, {"file": 49, "parent": 124}, {"command": 5, "file": 49, "line": 9, "parent": 125}, {"file": 48, "parent": 126}, {"command": 4, "file": 48, "line": 56, "parent": 127}, {"command": 5, "file": 52, "line": 41, "parent": 119}, {"file": 57, "parent": 129}, {"command": 5, "file": 57, "line": 9, "parent": 130}, {"file": 56, "parent": 131}, {"command": 4, "file": 56, "line": 56, "parent": 132}, {"command": 5, "file": 6, "line": 41, "parent": 9}, {"file": 61, "parent": 134}, {"command": 6, "file": 61, "line": 21, "parent": 135}, {"file": 60, "parent": 136}, {"command": 5, "file": 60, "line": 41, "parent": 137}, {"file": 59, "parent": 138}, {"command": 5, "file": 59, "line": 9, "parent": 139}, {"file": 58, "parent": 140}, {"command": 4, "file": 58, "line": 61, "parent": 141}, {"command": 5, "file": 59, "line": 9, "parent": 139}, {"file": 62, "parent": 143}, {"command": 4, "file": 62, "line": 61, "parent": 144}, {"command": 5, "file": 59, "line": 9, "parent": 139}, {"file": 63, "parent": 146}, {"command": 4, "file": 63, "line": 61, "parent": 147}, {"command": 5, "file": 59, "line": 9, "parent": 139}, {"file": 64, "parent": 149}, {"command": 4, "file": 64, "line": 61, "parent": 150}, {"command": 5, "file": 59, "line": 9, "parent": 139}, {"file": 65, "parent": 152}, {"command": 4, "file": 65, "line": 61, "parent": 153}, {"command": 5, "file": 59, "line": 9, "parent": 139}, {"file": 66, "parent": 155}, {"command": 4, "file": 66, "line": 61, "parent": 156}, {"command": 5, "file": 59, "line": 9, "parent": 139}, {"file": 67, "parent": 158}, {"command": 4, "file": 67, "line": 61, "parent": 159}, {"command": 5, "file": 59, "line": 9, "parent": 139}, {"file": 68, "parent": 161}, {"command": 4, "file": 68, "line": 61, "parent": 162}, {"command": 5, "file": 14, "line": 41, "parent": 27}, {"file": 76, "parent": 164}, {"command": 6, "file": 76, "line": 21, "parent": 165}, {"file": 75, "parent": 166}, {"command": 5, "file": 75, "line": 41, "parent": 167}, {"file": 74, "parent": 168}, {"command": 6, "file": 74, "line": 21, "parent": 169}, {"file": 73, "parent": 170}, {"command": 5, "file": 73, "line": 41, "parent": 171}, {"file": 72, "parent": 172}, {"command": 6, "file": 72, "line": 21, "parent": 173}, {"file": 71, "parent": 174}, {"command": 5, "file": 71, "line": 41, "parent": 175}, {"file": 70, "parent": 176}, {"command": 5, "file": 70, "line": 9, "parent": 177}, {"file": 69, "parent": 178}, {"command": 4, "file": 69, "line": 56, "parent": 179}, {"command": 6, "file": 72, "line": 21, "parent": 173}, {"file": 81, "parent": 181}, {"command": 5, "file": 81, "line": 41, "parent": 182}, {"file": 80, "parent": 183}, {"command": 6, "file": 80, "line": 21, "parent": 184}, {"file": 79, "parent": 185}, {"command": 5, "file": 79, "line": 41, "parent": 186}, {"file": 78, "parent": 187}, {"command": 5, "file": 78, "line": 9, "parent": 188}, {"file": 77, "parent": 189}, {"command": 4, "file": 77, "line": 56, "parent": 190}, {"command": 7, "file": 1, "line": 5, "parent": 0}, {"command": 8, "file": 0, "line": 166, "parent": 1}, {"command": 8, "file": 2, "line": 141, "parent": 4}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-O2 -g -DNDEBUG"}, {"backtrace": 192, "fragment": "-Wall"}, {"backtrace": 192, "fragment": "-Wextra"}, {"backtrace": 192, "fragment": "-Wpedantic"}, {"backtrace": 192, "fragment": "-Wconversion"}], "defines": [{"backtrace": 5, "define": "DEFAULT_RMW_IMPLEMENTATION=rmw_fastrtps_cpp"}, {"backtrace": 5, "define": "RCUTILS_ENABLE_FAULT_INJECTION"}], "includes": [{"backtrace": 193, "path": "/opt/ros/humble/src/gmock_vendor/include"}, {"backtrace": 193, "path": "/opt/ros/humble/src/gtest_vendor/include"}, {"backtrace": 194, "isSystem": true, "path": "/home/<USER>/code/ros2_control/install/include/controller_manager"}, {"backtrace": 194, "isSystem": true, "path": "/home/<USER>/code/ros2_control/install/include/hardware_interface"}, {"backtrace": 194, "isSystem": true, "path": "/home/<USER>/code/ros2_control/install/include/ros2_control_test_assets"}, {"backtrace": 194, "isSystem": true, "path": "/home/<USER>/code/ros2_control/install/include"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/ament_index_cpp"}, {"backtrace": 5, "isSystem": true, "path": "/home/<USER>/code/ros2_control/install/include/controller_interface"}, {"backtrace": 5, "isSystem": true, "path": "/home/<USER>/code/ros2_control/install/include/control_msgs"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/action_msgs"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/builtin_interfaces"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_runtime_c"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/rcutils"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_interface"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/unique_identifier_msgs"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/geometry_msgs"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/std_msgs"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/sensor_msgs"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/trajectory_msgs"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/fastcdr"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_runtime_cpp"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_fastrtps_cpp"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/rmw"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_fastrtps_c"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_introspection_c"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_introspection_cpp"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/lifecycle_msgs"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/pluginlib"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/class_loader"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/rcpputils"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/rclcpp_lifecycle"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/rclcpp"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/libstatistics_collector"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/rcl"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/rcl_interfaces"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/rcl_logging_interface"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/rcl_yaml_param_parser"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/libyaml_vendor"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/tracetools"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/statistics_msgs"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/rosgraph_msgs"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_cpp"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_c"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/rcl_lifecycle"}, {"backtrace": 5, "isSystem": true, "path": "/home/<USER>/code/ros2_control/install/include/controller_manager_msgs"}, {"backtrace": 5, "isSystem": true, "path": "/home/<USER>/code/ros2_control/install/include/realtime_tools"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/rclcpp_action"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/rcl_action"}], "language": "CXX", "sourceIndexes": [0]}], "dependencies": [{"backtrace": 3, "id": "gmock::@0590522bda6a4df7f28a"}], "id": "test_load_admittance_controller::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-O2 -g -DNDEBUG", "role": "flags"}, {"fragment": "-Wl,--no-as-needed  /opt/ros/humble/lib/libbackward.so -Wl,--as-needed", "role": "flags"}, {"fragment": "-Wl,-rpath,/home/<USER>/code/ros2_control/install/lib:/opt/ros/humble/lib", "role": "libraries"}, {"backtrace": 3, "fragment": "gmock/libgmock.a", "role": "libraries"}, {"backtrace": 5, "fragment": "/home/<USER>/code/ros2_control/install/lib/libcontroller_manager.so", "role": "libraries"}, {"backtrace": 7, "fragment": "-pthread", "role": "libraries"}, {"backtrace": 14, "fragment": "/home/<USER>/code/ros2_control/install/lib/libcontroller_interface.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/home/<USER>/code/ros2_control/install/lib/libfake_components.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/home/<USER>/code/ros2_control/install/lib/libmock_components.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/home/<USER>/code/ros2_control/install/lib/libhardware_interface.so", "role": "libraries"}, {"backtrace": 25, "fragment": "/home/<USER>/code/ros2_control/install/lib/libcontrol_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 32, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 32, "fragment": "/opt/ros/humble/lib/libtrajectory_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 32, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 25, "fragment": "/home/<USER>/code/ros2_control/install/lib/libcontrol_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 35, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 35, "fragment": "/opt/ros/humble/lib/libtrajectory_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 35, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 25, "fragment": "/home/<USER>/code/ros2_control/install/lib/libcontrol_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 38, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 38, "fragment": "/opt/ros/humble/lib/libtrajectory_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 38, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 25, "fragment": "/home/<USER>/code/ros2_control/install/lib/libcontrol_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 41, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 41, "fragment": "/opt/ros/humble/lib/libtrajectory_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 41, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 25, "fragment": "/home/<USER>/code/ros2_control/install/lib/libcontrol_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 44, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 44, "fragment": "/opt/ros/humble/lib/libtrajectory_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 44, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 25, "fragment": "/home/<USER>/code/ros2_control/install/lib/libcontrol_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 25, "fragment": "/home/<USER>/code/ros2_control/install/lib/libcontrol_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 25, "fragment": "/home/<USER>/code/ros2_control/install/lib/libcontrol_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 47, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 50, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 53, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 47, "fragment": "/opt/ros/humble/lib/libtrajectory_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 50, "fragment": "/opt/ros/humble/lib/libtrajectory_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 53, "fragment": "/opt/ros/humble/lib/libtrajectory_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 47, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 50, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 53, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 25, "fragment": "/opt/ros/humble/lib/librclcpp_lifecycle.so", "role": "libraries"}, {"backtrace": 64, "fragment": "/opt/ros/humble/lib/librcl_lifecycle.so", "role": "libraries"}, {"backtrace": 14, "fragment": "/home/<USER>/code/ros2_control/install/lib/libcontroller_manager_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 25, "fragment": "/opt/ros/humble/lib/liblifecycle_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 14, "fragment": "/home/<USER>/code/ros2_control/install/lib/libcontroller_manager_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 25, "fragment": "/opt/ros/humble/lib/liblifecycle_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 14, "fragment": "/home/<USER>/code/ros2_control/install/lib/libcontroller_manager_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 25, "fragment": "/opt/ros/humble/lib/liblifecycle_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 14, "fragment": "/home/<USER>/code/ros2_control/install/lib/libcontroller_manager_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 25, "fragment": "/opt/ros/humble/lib/liblifecycle_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 14, "fragment": "/home/<USER>/code/ros2_control/install/lib/libcontroller_manager_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 25, "fragment": "/opt/ros/humble/lib/liblifecycle_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 14, "fragment": "/home/<USER>/code/ros2_control/install/lib/libcontroller_manager_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 14, "fragment": "/home/<USER>/code/ros2_control/install/lib/libcontroller_manager_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 14, "fragment": "/home/<USER>/code/ros2_control/install/lib/libcontroller_manager_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 25, "fragment": "/opt/ros/humble/lib/liblifecycle_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 25, "fragment": "/opt/ros/humble/lib/liblifecycle_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 25, "fragment": "/opt/ros/humble/lib/liblifecycle_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 25, "fragment": "/usr/lib/x86_64-linux-gnu/libtinyxml2.so", "role": "libraries"}, {"backtrace": 75, "fragment": "/opt/ros/humble/lib/libclass_loader.so", "role": "libraries"}, {"backtrace": 84, "fragment": "/usr/lib/x86_64-linux-gnu/libconsole_bridge.so.1.0", "role": "libraries"}, {"backtrace": 14, "fragment": "/home/<USER>/code/ros2_control/install/lib/librealtime_tools.so", "role": "libraries"}, {"backtrace": 14, "fragment": "/home/<USER>/code/ros2_control/install/lib/libthread_priority.so", "role": "libraries"}, {"backtrace": 91, "fragment": "/opt/ros/humble/lib/librclcpp_action.so", "role": "libraries"}, {"backtrace": 14, "fragment": "/opt/ros/humble/lib/librclcpp.so", "role": "libraries"}, {"backtrace": 102, "fragment": "/opt/ros/humble/lib/liblibstatistics_collector.so", "role": "libraries"}, {"backtrace": 102, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 102, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 102, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 102, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 102, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 102, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 102, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 102, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 102, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 102, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 102, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 102, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 102, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 102, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 102, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 102, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 111, "fragment": "/opt/ros/humble/lib/librcl_action.so", "role": "libraries"}, {"backtrace": 102, "fragment": "/opt/ros/humble/lib/librcl.so", "role": "libraries"}, {"backtrace": 102, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 102, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 102, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 102, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 102, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 102, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 102, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 102, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 102, "fragment": "/opt/ros/humble/lib/librcl_yaml_param_parser.so", "role": "libraries"}, {"backtrace": 128, "fragment": "/opt/ros/humble/lib/libyaml.so", "role": "libraries"}, {"backtrace": 102, "fragment": "/opt/ros/humble/lib/libtracetools.so", "role": "libraries"}, {"backtrace": 133, "fragment": "/opt/ros/humble/lib/librmw_implementation.so", "role": "libraries"}, {"backtrace": 14, "fragment": "/opt/ros/humble/lib/libament_index_cpp.so", "role": "libraries"}, {"backtrace": 133, "fragment": "/opt/ros/humble/lib/librcl_logging_spdlog.so", "role": "libraries"}, {"backtrace": 133, "fragment": "/opt/ros/humble/lib/librcl_logging_interface.so", "role": "libraries"}, {"backtrace": 32, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 32, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 35, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 35, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 38, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 38, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 41, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 41, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 44, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 44, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 47, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 50, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 53, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 47, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 50, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 53, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 91, "fragment": "-lcap", "role": "libraries"}, {"backtrace": 14, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 142, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 142, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 14, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 145, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 142, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 142, "fragment": "/opt/ros/humble/lib/libfastcdr.so.1.0.24", "role": "libraries"}, {"backtrace": 145, "fragment": "/opt/ros/humble/lib/librmw.so", "role": "libraries"}, {"backtrace": 14, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 148, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 14, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 151, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 151, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 148, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 14, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 154, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 102, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 14, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 14, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 14, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 157, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 160, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 163, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 180, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 25, "fragment": "/opt/ros/humble/lib/librcpputils.so", "role": "libraries"}, {"backtrace": 163, "fragment": "/opt/ros/humble/lib/librosidl_runtime_c.so", "role": "libraries"}, {"backtrace": 25, "fragment": "/opt/ros/humble/lib/librcutils.so", "role": "libraries"}, {"backtrace": 191, "fragment": "-ldl", "role": "libraries"}, {"backtrace": 157, "fragment": "/usr/lib/x86_64-linux-gnu/libpython3.10.so", "role": "libraries"}], "language": "CXX"}, "name": "test_load_admittance_controller", "nameOnDisk": "test_load_admittance_controller", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 2, "compileGroupIndex": 0, "path": "test/test_load_admittance_controller.cpp", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}