{"archive": {}, "artifacts": [{"path": "gtest/libgtest.a"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "add_compile_options", "target_compile_options", "_ament_cmake_gtest_find_gtest", "ament_find_gtest", "_ament_cmake_gmock_find_gmock", "add_rostest_with_parameters_gmock", "include_directories", "target_include_directories"], "files": ["/opt/ros/humble/src/gtest_vendor/CMakeLists.txt", "CMakeLists.txt", "/opt/ros/humble/share/ament_cmake_gtest/cmake/ament_cmake_gtest-extras.cmake", "/opt/ros/humble/share/ament_cmake_gtest/cmake/ament_find_gtest.cmake", "/opt/ros/humble/share/ament_cmake_gmock/cmake/ament_cmake_gmock-extras.cmake", "/opt/ros/humble/share/generate_parameter_library/cmake/generate_parameter_library.cmake"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 9, "parent": 0}, {"file": 1}, {"command": 1, "file": 1, "line": 5, "parent": 2}, {"command": 6, "file": 1, "line": 69, "parent": 2}, {"command": 5, "file": 5, "line": 165, "parent": 4}, {"command": 4, "file": 4, "line": 104, "parent": 5}, {"command": 3, "file": 3, "line": 33, "parent": 6}, {"command": 2, "file": 2, "line": 91, "parent": 7}, {"command": 7, "file": 0, "line": 4, "parent": 0}, {"command": 8, "file": 2, "line": 93, "parent": 7}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-O2 -g -DNDEBUG -fPIC"}, {"fragment": "-Wno-missing-field-initializers"}, {"backtrace": 3, "fragment": "-Wall"}, {"backtrace": 3, "fragment": "-Wextra"}, {"backtrace": 3, "fragment": "-Wpedantic"}, {"backtrace": 3, "fragment": "-Wconversion"}, {"backtrace": 8, "fragment": "-Wno-null-dereference"}], "includes": [{"backtrace": 9, "path": "/opt/ros/humble/src/gtest_vendor/."}, {"backtrace": 10, "isSystem": true, "path": "/opt/ros/humble/src/gtest_vendor/include"}], "language": "CXX", "sourceIndexes": [0]}], "id": "gtest::@0af4add9fe94340e763e", "name": "gtest", "nameOnDisk": "libgtest.a", "paths": {"build": "gtest", "source": "/opt/ros/humble/src/gtest_vendor"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "/opt/ros/humble/src/gtest_vendor/src/gtest-all.cc", "sourceGroupIndex": 0}], "type": "STATIC_LIBRARY"}