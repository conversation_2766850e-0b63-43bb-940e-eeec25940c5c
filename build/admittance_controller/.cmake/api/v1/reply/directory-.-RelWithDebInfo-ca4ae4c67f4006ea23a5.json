{"backtraceGraph": {"commands": ["_install", "install", "include", "find_package", "ament_cmake_symlink_install_targets", "ament_execute_extensions", "ament_package"], "files": ["/opt/ros/humble/share/ament_cmake_core/cmake/symlink_install/install.cmake", "/opt/ros/humble/share/ament_cmake_core/cmake/ament_cmake_symlink_install-extras.cmake", "/opt/ros/humble/share/ament_cmake_core/cmake/ament_cmake_coreConfig.cmake", "/opt/ros/humble/share/ament_cmake/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/ament_cmake/cmake/ament_cmakeConfig.cmake", "CMakeLists.txt", "/opt/ros/humble/share/ament_cmake_core/cmake/symlink_install/ament_cmake_symlink_install_targets.cmake", "/opt/ros/humble/share/ament_cmake_export_targets/cmake/ament_cmake_export_targets_package_hook.cmake", "/opt/ros/humble/share/ament_cmake_core/cmake/core/ament_execute_extensions.cmake", "/opt/ros/humble/share/ament_cmake_core/cmake/core/ament_package.cmake"], "nodes": [{"file": 5}, {"command": 3, "file": 5, "line": 29, "parent": 0}, {"file": 4, "parent": 1}, {"command": 2, "file": 4, "line": 41, "parent": 2}, {"file": 3, "parent": 3}, {"command": 3, "file": 3, "line": 15, "parent": 4}, {"file": 2, "parent": 5}, {"command": 2, "file": 2, "line": 41, "parent": 6}, {"file": 1, "parent": 7}, {"command": 1, "file": 1, "line": 47, "parent": 8}, {"command": 0, "file": 0, "line": 43, "parent": 9}, {"command": 1, "file": 5, "line": 98, "parent": 0}, {"command": 4, "file": 0, "line": 37, "parent": 11}, {"command": 0, "file": 6, "line": 50, "parent": 12}, {"command": 6, "file": 5, "line": 107, "parent": 0}, {"command": 5, "file": 9, "line": 66, "parent": 14}, {"command": 2, "file": 8, "line": 48, "parent": 15}, {"file": 7, "parent": 16}, {"command": 1, "file": 7, "line": 28, "parent": 17}, {"command": 0, "file": 0, "line": 43, "parent": 18}]}, "installers": [{"backtrace": 10, "component": "Unspecified", "scriptFile": "/home/<USER>/code/ros2_control/build/admittance_controller/ament_cmake_symlink_install/ament_cmake_symlink_install.cmake", "type": "script"}, {"backtrace": 13, "component": "Unspecified", "destination": "lib", "paths": ["libadmittance_controller.so"], "targetId": "admittance_controller::@6890427a1f51a3e7e1df", "targetIndex": 0, "type": "target"}, {"backtrace": 19, "component": "Unspecified", "destination": "share/admittance_controller/cmake", "exportName": "export_admittance_controller", "exportTargets": [{"id": "admittance_controller::@6890427a1f51a3e7e1df", "index": 0}, {"id": "admittance_controller_parameters::@6890427a1f51a3e7e1df", "index": 1}], "paths": ["CMakeFiles/Export/538dd8a1dadc382549f3ec076ffc9f21/export_admittance_controllerExport.cmake"], "type": "export"}], "paths": {"build": ".", "source": "."}}