#----------------------------------------------------------------
# Generated CMake target import file for configuration "RelWithDebInfo".
#----------------------------------------------------------------

# Commands may need to know the format version.
set(CMAKE_IMPORT_FILE_VERSION 1)

# Import target "admittance_controller::admittance_controller" for configuration "RelWithDebInfo"
set_property(TARGET admittance_controller::admittance_controller APPEND PROPERTY IMPORTED_CONFIGURATIONS RELWITHDEBINFO)
set_target_properties(admittance_controller::admittance_controller PROPERTIES
  IMPORTED_LOCATION_RELWITHDEBINFO "${_IMPORT_PREFIX}/lib/libadmittance_controller.so"
  IMPORTED_SONAME_RELWITHDEBINFO "libadmittance_controller.so"
  )

list(APPEND _cmake_import_check_targets admittance_controller::admittance_controller )
list(APPEND _cmake_import_check_files_for_admittance_controller::admittance_controller "${_IMPORT_PREFIX}/lib/libadmittance_controller.so" )

# Commands beyond this point should not need to know the version.
set(CMAKE_IMPORT_FILE_VERSION)
