# Generated by C<PERSON>ake

if("${CMAKE_MAJOR_VERSION}.${CMAKE_MINOR_VERSION}" LESS 2.8)
   message(FATAL_ERROR "CMake >= 3.0.0 required")
endif()
if(CMAKE_VERSION VERSION_LESS "3.0.0")
   message(FATAL_ERROR "CMake >= 3.0.0 required")
endif()
cmake_policy(PUSH)
cmake_policy(VERSION 3.0.0...3.31)
#----------------------------------------------------------------
# Generated CMake target import file.
#----------------------------------------------------------------

# Commands may need to know the format version.
set(CMAKE_IMPORT_FILE_VERSION 1)

# Protect against multiple inclusion, which would fail when already imported targets are added once more.
set(_cmake_targets_defined "")
set(_cmake_targets_not_defined "")
set(_cmake_expected_targets "")
foreach(_cmake_expected_target IN ITEMS admittance_controller::admittance_controller admittance_controller::admittance_controller_parameters)
  list(APPEND _cmake_expected_targets "${_cmake_expected_target}")
  if(TARGET "${_cmake_expected_target}")
    list(APPEND _cmake_targets_defined "${_cmake_expected_target}")
  else()
    list(APPEND _cmake_targets_not_defined "${_cmake_expected_target}")
  endif()
endforeach()
unset(_cmake_expected_target)
if(_cmake_targets_defined STREQUAL _cmake_expected_targets)
  unset(_cmake_targets_defined)
  unset(_cmake_targets_not_defined)
  unset(_cmake_expected_targets)
  unset(CMAKE_IMPORT_FILE_VERSION)
  cmake_policy(POP)
  return()
endif()
if(NOT _cmake_targets_defined STREQUAL "")
  string(REPLACE ";" ", " _cmake_targets_defined_text "${_cmake_targets_defined}")
  string(REPLACE ";" ", " _cmake_targets_not_defined_text "${_cmake_targets_not_defined}")
  message(FATAL_ERROR "Some (but not all) targets in this export set were already defined.\nTargets Defined: ${_cmake_targets_defined_text}\nTargets not yet defined: ${_cmake_targets_not_defined_text}\n")
endif()
unset(_cmake_targets_defined)
unset(_cmake_targets_not_defined)
unset(_cmake_expected_targets)


# Compute the installation prefix relative to this file.
get_filename_component(_IMPORT_PREFIX "${CMAKE_CURRENT_LIST_FILE}" PATH)
get_filename_component(_IMPORT_PREFIX "${_IMPORT_PREFIX}" PATH)
get_filename_component(_IMPORT_PREFIX "${_IMPORT_PREFIX}" PATH)
get_filename_component(_IMPORT_PREFIX "${_IMPORT_PREFIX}" PATH)
if(_IMPORT_PREFIX STREQUAL "/")
  set(_IMPORT_PREFIX "")
endif()

# Create imported target admittance_controller::admittance_controller
add_library(admittance_controller::admittance_controller SHARED IMPORTED)

set_target_properties(admittance_controller::admittance_controller PROPERTIES
  INTERFACE_COMPILE_FEATURES "cxx_std_17"
  INTERFACE_INCLUDE_DIRECTORIES "${_IMPORT_PREFIX}/include/admittance_controller"
  INTERFACE_LINK_LIBRARIES "admittance_controller::admittance_controller_parameters;Eigen3::Eigen;angles::angles;control_msgs::control_msgs__rosidl_generator_c;control_msgs::control_msgs__rosidl_typesupport_fastrtps_c;control_msgs::control_msgs__rosidl_generator_cpp;control_msgs::control_msgs__rosidl_typesupport_fastrtps_cpp;control_msgs::control_msgs__rosidl_typesupport_introspection_c;control_msgs::control_msgs__rosidl_typesupport_c;control_msgs::control_msgs__rosidl_typesupport_introspection_cpp;control_msgs::control_msgs__rosidl_typesupport_cpp;control_msgs::control_msgs__rosidl_generator_py;control_toolbox::control_toolbox;control_toolbox::low_pass_filter;control_toolbox::low_pass_filter_parameters;control_toolbox::rate_limiter;control_toolbox::rate_limiter_parameters;control_toolbox::exponential_filter;control_toolbox::exponential_filter_parameters;controller_interface::controller_interface;geometry_msgs::geometry_msgs__rosidl_generator_c;geometry_msgs::geometry_msgs__rosidl_typesupport_fastrtps_c;geometry_msgs::geometry_msgs__rosidl_typesupport_introspection_c;geometry_msgs::geometry_msgs__rosidl_typesupport_c;geometry_msgs::geometry_msgs__rosidl_generator_cpp;geometry_msgs::geometry_msgs__rosidl_typesupport_fastrtps_cpp;geometry_msgs::geometry_msgs__rosidl_typesupport_introspection_cpp;geometry_msgs::geometry_msgs__rosidl_typesupport_cpp;geometry_msgs::geometry_msgs__rosidl_generator_py;hardware_interface::fake_components;hardware_interface::mock_components;hardware_interface::hardware_interface;kinematics_interface::kinematics_interface;pluginlib::pluginlib;rclcpp::rclcpp;rclcpp_lifecycle::rclcpp_lifecycle;realtime_tools::realtime_tools;realtime_tools::thread_priority;tf2::tf2;tf2_eigen::tf2_eigen;tf2_geometry_msgs::tf2_geometry_msgs;tf2_kdl::tf2_kdl;tf2_ros::tf2_ros;tf2_ros::static_transform_broadcaster_node;trajectory_msgs::trajectory_msgs__rosidl_generator_c;trajectory_msgs::trajectory_msgs__rosidl_typesupport_fastrtps_c;trajectory_msgs::trajectory_msgs__rosidl_generator_cpp;trajectory_msgs::trajectory_msgs__rosidl_typesupport_fastrtps_cpp;trajectory_msgs::trajectory_msgs__rosidl_typesupport_introspection_c;trajectory_msgs::trajectory_msgs__rosidl_typesupport_c;trajectory_msgs::trajectory_msgs__rosidl_typesupport_introspection_cpp;trajectory_msgs::trajectory_msgs__rosidl_typesupport_cpp;trajectory_msgs::trajectory_msgs__rosidl_generator_py"
)

# Create imported target admittance_controller::admittance_controller_parameters
add_library(admittance_controller::admittance_controller_parameters INTERFACE IMPORTED)

set_target_properties(admittance_controller::admittance_controller_parameters PROPERTIES
  INTERFACE_INCLUDE_DIRECTORIES "${_IMPORT_PREFIX}/include"
  INTERFACE_LINK_LIBRARIES "fmt::fmt;parameter_traits::parameter_traits;rclcpp::rclcpp;rclcpp_lifecycle::rclcpp_lifecycle;rsl::rsl;tcb_span::tcb_span;tl_expected::tl_expected"
)

# Load information for each installed configuration.
file(GLOB _cmake_config_files "${CMAKE_CURRENT_LIST_DIR}/export_admittance_controllerExport-*.cmake")
foreach(_cmake_config_file IN LISTS _cmake_config_files)
  include("${_cmake_config_file}")
endforeach()
unset(_cmake_config_file)
unset(_cmake_config_files)

# Cleanup temporary variables.
set(_IMPORT_PREFIX)

# Loop over all imported files and verify that they actually exist
foreach(_cmake_target IN LISTS _cmake_import_check_targets)
  if(CMAKE_VERSION VERSION_LESS "3.28"
      OR NOT DEFINED _cmake_import_check_xcframework_for_${_cmake_target}
      OR NOT IS_DIRECTORY "${_cmake_import_check_xcframework_for_${_cmake_target}}")
    foreach(_cmake_file IN LISTS "_cmake_import_check_files_for_${_cmake_target}")
      if(NOT EXISTS "${_cmake_file}")
        message(FATAL_ERROR "The imported target \"${_cmake_target}\" references the file
   \"${_cmake_file}\"
but this file does not exist.  Possible reasons include:
* The file was deleted, renamed, or moved to another location.
* An install or uninstall procedure did not complete successfully.
* The installation package was faulty and contained
   \"${CMAKE_CURRENT_LIST_FILE}\"
but not all the files it references.
")
      endif()
    endforeach()
  endif()
  unset(_cmake_file)
  unset("_cmake_import_check_files_for_${_cmake_target}")
endforeach()
unset(_cmake_target)
unset(_cmake_import_check_targets)

# This file does not depend on other imported targets which have
# been exported from the same project but in a separate export set.

# Commands beyond this point should not need to know the version.
set(CMAKE_IMPORT_FILE_VERSION)
cmake_policy(POP)
