# CMake generated Testfile for 
# Source directory: /home/<USER>/code/ros2_control/src/ros-controls/ros2_controllers/ackermann_steering_controller
# Build directory: /home/<USER>/code/ros2_control/build/ackermann_steering_controller
# 
# This file includes the relevant testing commands required for 
# testing this directory and lists subdirectories to be tested as well.
add_test(test_load_ackermann_steering_controller "/usr/bin/python3" "-u" "/opt/ros/humble/share/ament_cmake_test/cmake/run_test.py" "/home/<USER>/code/ros2_control/build/ackermann_steering_controller/test_results/ackermann_steering_controller/test_load_ackermann_steering_controller.gtest.xml" "--package-name" "ackermann_steering_controller" "--output-file" "/home/<USER>/code/ros2_control/build/ackermann_steering_controller/test_results/ackermann_steering_controller/test_load_ackermann_steering_controller.txt" "--command" "/home/<USER>/code/ros2_control/build/acker<PERSON>_steering_controller/test_load_ackermann_steering_controller" "--ros-args" "--params-file" "/home/<USER>/code/ros2_control/src/ros-controls/ros2_controllers/ackermann_steering_controller/test/ackermann_steering_controller_params.yaml" "--" "--gtest_output=xml:/home/<USER>/code/ros2_control/build/ackermann_steering_controller/test_results/ackermann_steering_controller/test_load_ackermann_steering_controller.gtest.xml")
set_tests_properties(test_load_ackermann_steering_controller PROPERTIES  TIMEOUT "60" WORKING_DIRECTORY "/home/<USER>/code/ros2_control/build/ackermann_steering_controller" _BACKTRACE_TRIPLES "/opt/ros/humble/share/ament_cmake_test/cmake/ament_add_test.cmake;125;add_test;/opt/ros/humble/share/generate_parameter_library/cmake/generate_parameter_library.cmake;170;ament_add_test;/home/<USER>/code/ros2_control/src/ros-controls/ros2_controllers/ackermann_steering_controller/CMakeLists.txt;58;add_rostest_with_parameters_gmock;/home/<USER>/code/ros2_control/src/ros-controls/ros2_controllers/ackermann_steering_controller/CMakeLists.txt;0;")
add_test(test_ackermann_steering_controller "/usr/bin/python3" "-u" "/opt/ros/humble/share/ament_cmake_test/cmake/run_test.py" "/home/<USER>/code/ros2_control/build/ackermann_steering_controller/test_results/ackermann_steering_controller/test_ackermann_steering_controller.gtest.xml" "--package-name" "ackermann_steering_controller" "--output-file" "/home/<USER>/code/ros2_control/build/ackermann_steering_controller/test_results/ackermann_steering_controller/test_ackermann_steering_controller.txt" "--command" "/home/<USER>/code/ros2_control/build/ackermann_steering_controller/test_ackermann_steering_controller" "--ros-args" "--params-file" "/home/<USER>/code/ros2_control/src/ros-controls/ros2_controllers/ackermann_steering_controller/test/ackermann_steering_controller_params.yaml" "--" "--gtest_output=xml:/home/<USER>/code/ros2_control/build/ackermann_steering_controller/test_results/ackermann_steering_controller/test_ackermann_steering_controller.gtest.xml")
set_tests_properties(test_ackermann_steering_controller PROPERTIES  TIMEOUT "60" WORKING_DIRECTORY "/home/<USER>/code/ros2_control/build/ackermann_steering_controller" _BACKTRACE_TRIPLES "/opt/ros/humble/share/ament_cmake_test/cmake/ament_add_test.cmake;125;add_test;/opt/ros/humble/share/generate_parameter_library/cmake/generate_parameter_library.cmake;170;ament_add_test;/home/<USER>/code/ros2_control/src/ros-controls/ros2_controllers/ackermann_steering_controller/CMakeLists.txt;68;add_rostest_with_parameters_gmock;/home/<USER>/code/ros2_control/src/ros-controls/ros2_controllers/ackermann_steering_controller/CMakeLists.txt;0;")
add_test(test_ackermann_steering_controller_preceding "/usr/bin/python3" "-u" "/opt/ros/humble/share/ament_cmake_test/cmake/run_test.py" "/home/<USER>/code/ros2_control/build/ackermann_steering_controller/test_results/ackermann_steering_controller/test_ackermann_steering_controller_preceding.gtest.xml" "--package-name" "ackermann_steering_controller" "--output-file" "/home/<USER>/code/ros2_control/build/ackermann_steering_controller/test_results/ackermann_steering_controller/test_ackermann_steering_controller_preceding.txt" "--command" "/home/<USER>/code/ros2_control/build/ackermann_steering_controller/test_ackermann_steering_controller_preceding" "--ros-args" "--params-file" "/home/<USER>/code/ros2_control/src/ros-controls/ros2_controllers/ackermann_steering_controller/test/ackermann_steering_controller_preceding_params.yaml" "--" "--gtest_output=xml:/home/<USER>/code/ros2_control/build/ackermann_steering_controller/test_results/ackermann_steering_controller/test_ackermann_steering_controller_preceding.gtest.xml")
set_tests_properties(test_ackermann_steering_controller_preceding PROPERTIES  TIMEOUT "60" WORKING_DIRECTORY "/home/<USER>/code/ros2_control/build/ackermann_steering_controller" _BACKTRACE_TRIPLES "/opt/ros/humble/share/ament_cmake_test/cmake/ament_add_test.cmake;125;add_test;/opt/ros/humble/share/generate_parameter_library/cmake/generate_parameter_library.cmake;170;ament_add_test;/home/<USER>/code/ros2_control/src/ros-controls/ros2_controllers/ackermann_steering_controller/CMakeLists.txt;80;add_rostest_with_parameters_gmock;/home/<USER>/code/ros2_control/src/ros-controls/ros2_controllers/ackermann_steering_controller/CMakeLists.txt;0;")
subdirs("gmock")
subdirs("gtest")
