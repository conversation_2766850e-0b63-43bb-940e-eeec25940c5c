# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.1

# compile CXX with /usr/bin/c++
CXX_DEFINES = -DDEFAULT_RMW_IMPLEMENTATION=rmw_fastrtps_cpp -DRCUTILS_ENABLE_FAULT_INJECTION

CXX_INCLUDES = -I/opt/ros/humble/src/gmock_vendor/include -I/opt/ros/humble/src/gtest_vendor/include -isystem /home/<USER>/code/ros2_control/install/include/controller_manager -isystem /home/<USER>/code/ros2_control/install/include/hardware_interface -isystem /home/<USER>/code/ros2_control/install/include/ros2_control_test_assets -isystem /home/<USER>/code/ros2_control/install/include -isystem /opt/ros/humble/include/ament_index_cpp -isystem /home/<USER>/code/ros2_control/install/include/controller_interface -isystem /home/<USER>/code/ros2_control/install/include/control_msgs -isystem /opt/ros/humble/include/action_msgs -isystem /opt/ros/humble/include/builtin_interfaces -isystem /opt/ros/humble/include/rosidl_runtime_c -isystem /opt/ros/humble/include/rcutils -isystem /opt/ros/humble/include/rosidl_typesupport_interface -isystem /opt/ros/humble/include/unique_identifier_msgs -isystem /opt/ros/humble/include/geometry_msgs -isystem /opt/ros/humble/include/std_msgs -isystem /opt/ros/humble/include/sensor_msgs -isystem /opt/ros/humble/include/trajectory_msgs -isystem /opt/ros/humble/include/fastcdr -isystem /opt/ros/humble/include/rosidl_runtime_cpp -isystem /opt/ros/humble/include/rosidl_typesupport_fastrtps_cpp -isystem /opt/ros/humble/include/rmw -isystem /opt/ros/humble/include/rosidl_typesupport_fastrtps_c -isystem /opt/ros/humble/include/rosidl_typesupport_introspection_c -isystem /opt/ros/humble/include/rosidl_typesupport_introspection_cpp -isystem /opt/ros/humble/include/lifecycle_msgs -isystem /opt/ros/humble/include/pluginlib -isystem /opt/ros/humble/include/class_loader -isystem /opt/ros/humble/include/rcpputils -isystem /opt/ros/humble/include/rclcpp_lifecycle -isystem /opt/ros/humble/include/rclcpp -isystem /opt/ros/humble/include/libstatistics_collector -isystem /opt/ros/humble/include/rcl -isystem /opt/ros/humble/include/rcl_interfaces -isystem /opt/ros/humble/include/rcl_logging_interface -isystem /opt/ros/humble/include/rcl_yaml_param_parser -isystem /opt/ros/humble/include/libyaml_vendor -isystem /opt/ros/humble/include/tracetools -isystem /opt/ros/humble/include/statistics_msgs -isystem /opt/ros/humble/include/rosgraph_msgs -isystem /opt/ros/humble/include/rosidl_typesupport_cpp -isystem /opt/ros/humble/include/rosidl_typesupport_c -isystem /opt/ros/humble/include/rcl_lifecycle -isystem /home/<USER>/code/ros2_control/install/include/controller_manager_msgs -isystem /home/<USER>/code/ros2_control/install/include/realtime_tools -isystem /opt/ros/humble/include/rclcpp_action -isystem /opt/ros/humble/include/rcl_action

CXX_FLAGS = -O2 -g -DNDEBUG -Wall -Wextra -Wpedantic -Wconversion

