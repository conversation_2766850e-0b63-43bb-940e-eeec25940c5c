AMENT_CPPCHECK_ALLOW_SLOW_VERSIONS=true
AMENT_PREFIX_PATH=/home/<USER>/code/ws_motor_driver/install:/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description:/opt/ros/humble
ARCHFLAGS=-arch x86_64
CMAKE_PREFIX_PATH=/home/<USER>/code/ros2_control/install:/home/<USER>/code/ws_motor_driver/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description
COLCON=1
COLCON_PREFIX_PATH=/home/<USER>/code/ws_motor_driver/install:/home/<USER>/code/ws_moveit2/install:/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install:/home/<USER>/code/ws_tools/install:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install
COLORTERM=truecolor
DBUS_SESSION_BUS_ADDRESS=unix:path=/run/user/1000/bus
DESKTOP_SESSION=ubuntu
DISPLAY=:0
EDITOR=mvim
FZF_DEFAULT_COMMAND=ag --hidden --ignore .git -l -g ""
FZF_DEFAULT_OPTS=--height 40% --layout=reverse --preview '(highlight -O ansi {} || cat {}) 2> /dev/null | head -500'
GAZEBO_MASTER_URI=http://localhost:11345
GAZEBO_MODEL_DATABASE_URI=http://models.gazebosim.org
GAZEBO_MODEL_PATH=/usr/share/gazebo/../../share/gazebo-11/models:
GAZEBO_PLUGIN_PATH=/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:
GAZEBO_RESOURCE_PATH=/usr/share/gazebo/../../share/gazebo-11:
GDMSESSION=ubuntu
GIO_LAUNCHED_DESKTOP_FILE_PID=128070
GNOME_DESKTOP_SESSION_ID=this-is-deprecated
GNOME_SETUP_DISPLAY=:1
GNOME_SHELL_SESSION_MODE=ubuntu
GTK_MODULES=gail:atk-bridge
HISTFILESIZE=2147450879
HISTSIZE=2147450879
HOME=/home/<USER>
IM_CONFIG_PHASE=1
INVOCATION_ID=60834c1b0efc43a6a4fffa43566f9939
JOURNAL_STREAM=8:34473
KITTY_WINDOW_ID=1
LANG=en_US.UTF-8
LANGUAGE=en_US:en
LC_ADDRESS=en_US.UTF-8
LC_ALL=en_US.UTF-8
LC_IDENTIFICATION=en_US.UTF-8
LC_MEASUREMENT=en_US.UTF-8
LC_MONETARY=en_US.UTF-8
LC_NAME=en_US.UTF-8
LC_NUMERIC=en_US.UTF-8
LC_PAPER=en_US.UTF-8
LC_TELEPHONE=en_US.UTF-8
LC_TIME=en_US.UTF-8
LD_LIBRARY_PATH=/home/<USER>/code/ros2_control/install/lib:/home/<USER>/code/ws_motor_driver/install/lib:/home/<USER>/code/ecat_ws/install/ethercat_manager/lib:/home/<USER>/code/ecat_ws/install/ethercat_msgs/lib:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/lib:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/lib:/home/<USER>/code/ecat_ws/install/ethercat_driver/lib:/home/<USER>/code/ecat_ws/install/ethercat_interface/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins
LESS=-R
LESSCLOSE=/usr/bin/lesspipe %s %s
LESSOPEN=| /usr/bin/lesspipe %s
LOGNAME=hq
LSCOLORS=Gxfxcxdxdxegedabagacad
LS_COLORS=rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:
MANAGERPID=2300
OGRE_RESOURCE_PATH=/usr/lib/x86_64-linux-gnu/OGRE-1.9.0
OLDPWD=/home/<USER>/code/ros2_control/src
OSH=/home/<USER>/.oh-my-bash
PAGER=less
PAPERSIZE=letter
PATH=/home/<USER>/code/ws_motor_driver/install/bin:/opt/ros/humble/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.fzf/bin
PKG_CONFIG_PATH=/home/<USER>/code/ws_motor_driver/install/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/code/ws_motor_driver/install/lib/pkgconfig
PWD=/home/<USER>/code/ros2_control/build/ackermann_steering_controller
PYTHONPATH=/home/<USER>/code/ros2_control/install/lib/python3.10/site-packages:/home/<USER>/code/ros2_control/build/rqt_joint_trajectory_controller:/home/<USER>/code/ros2_control/build/rqt_controller_manager:/home/<USER>/code/ros2_control/build/ros2controlcli:/home/<USER>/code/ros2_control/build/ros2_controllers_test_nodes:/home/<USER>/code/ros2_control/install/local/lib/python3.10/dist-packages:/home/<USER>/code/ecat_ws/install/ethercat_msgs/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages
QT_ACCESSIBILITY=1
QT_IM_MODULE=ibus
RAW_TERMINAL_COLOR_BLACK=[0;30m
RAW_TERMINAL_COLOR_BLUE=[0;34m
RAW_TERMINAL_COLOR_BROWN=[0;33m
RAW_TERMINAL_COLOR_CYAN=[0;36m
RAW_TERMINAL_COLOR_GRAY=[1;30m
RAW_TERMINAL_COLOR_GREEN=[0;32m
RAW_TERMINAL_COLOR_LIGHT_BLUE=[1;34m
RAW_TERMINAL_COLOR_LIGHT_CYAN=[1;36m
RAW_TERMINAL_COLOR_LIGHT_GRAY=[0;37m
RAW_TERMINAL_COLOR_LIGHT_GREEN=[1;32m
RAW_TERMINAL_COLOR_LIGHT_PURPLE=[1;35m
RAW_TERMINAL_COLOR_LIGHT_RED=[1;31m
RAW_TERMINAL_COLOR_NC=[0m
RAW_TERMINAL_COLOR_PURPLE=[0;35m
RAW_TERMINAL_COLOR_RED=[0;31m
RAW_TERMINAL_COLOR_WHITE=[1;37m
RAW_TERMINAL_COLOR_YELLOW=[1;33m
RCUTILS_COLORIZED_OUTPUT=1
ROS_DISTRO=humble
ROS_DOMAIN_ID=0
ROS_LOCALHOST_ONLY=0
ROS_PACKAGE_PATH=/home/<USER>/code/ws_motor_driver/install/share
ROS_PYTHON_VERSION=3
ROS_VERSION=2
ROS_WS=/home/<USER>/code/ws_motor_driver
ROS_WS_CACHE_SOURCED_TIME=0
RTI_LICENSE_FILE=/opt/rti.com/rti_connext_dds-5.3.1/rti_license.dat
RTW_COLOR_ERROR=\e[0;31m
RTW_COLOR_NOTIFY_USER=\e[1;33m
SESSION_MANAGER=local/ubuntu2204:@/tmp/.ICE-unix/2513,unix/ubuntu2204:/tmp/.ICE-unix/2513
SHELL=/usr/bin/bash
SHLVL=2
SSH_AGENT_LAUNCHER=gnome-keyring
SSH_AUTH_SOCK=/run/user/1000/keyring/ssh
SSH_KEY_PATH=~/.ssh/rsa_id
SYSTEMD_EXEC_PID=2759
TERM=xterm-kitty
TERMINAL_BG_COLOR_BLACK=\e[40m
TERMINAL_BG_COLOR_BLUE=\e[44m
TERMINAL_BG_COLOR_BROWN=\e[43m
TERMINAL_BG_COLOR_CYAN=\e[46m
TERMINAL_BG_COLOR_GRAY=\e[1;40m
TERMINAL_BG_COLOR_GREEN=\e[42m
TERMINAL_BG_COLOR_LIGHT_BLUE=\e[1;44m
TERMINAL_BG_COLOR_LIGHT_CYAN=\e[1;46m
TERMINAL_BG_COLOR_LIGHT_GRAY=\e[47m
TERMINAL_BG_COLOR_LIGHT_GREEN=\e[1;42m
TERMINAL_BG_COLOR_LIGHT_PURPLE=\e[1;45m
TERMINAL_BG_COLOR_LIGHT_RED=\e[1;41m
TERMINAL_BG_COLOR_PURPLE=\e[45m
TERMINAL_BG_COLOR_RED=\e[41m
TERMINAL_BG_COLOR_WHITE=\e[1;47m
TERMINAL_BG_COLOR_YELLOW=\e[1;43m
TERMINAL_COLOR_BLACK=\e[0;30m
TERMINAL_COLOR_BLUE=\e[0;34m
TERMINAL_COLOR_BROWN=\e[0;33m
TERMINAL_COLOR_CYAN=\e[0;36m
TERMINAL_COLOR_GRAY=\e[1;30m
TERMINAL_COLOR_GREEN=\e[0;32m
TERMINAL_COLOR_LIGHT_BLUE=\e[1;34m
TERMINAL_COLOR_LIGHT_CYAN=\e[1;36m
TERMINAL_COLOR_LIGHT_GRAY=\e[0;37m
TERMINAL_COLOR_LIGHT_GREEN=\e[1;32m
TERMINAL_COLOR_LIGHT_PURPLE=\e[1;35m
TERMINAL_COLOR_LIGHT_RED=\e[1;31m
TERMINAL_COLOR_NC=\e[0m
TERMINAL_COLOR_PURPLE=\e[0;35m
TERMINAL_COLOR_RED=\e[0;31m
TERMINAL_COLOR_USER_CONFIRMATION=\e[0;34m
TERMINAL_COLOR_USER_INPUT_DECISION=\e[0;35m
TERMINAL_COLOR_USER_NOTICE=\e[1;33m
TERMINAL_COLOR_WHITE=\e[1;37m
TERMINAL_COLOR_YELLOW=\e[1;33m
USER=hq
USERNAME=hq
WAYLAND_DISPLAY=wayland-0
XAUTHORITY=/run/user/1000/.mutter-Xwaylandauth.JV7IC3
XDG_CONFIG_DIRS=/etc/xdg/xdg-ubuntu:/etc/xdg
XDG_CURRENT_DESKTOP=ubuntu:GNOME
XDG_DATA_DIRS=/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop
XDG_MENU_PREFIX=gnome-
XDG_RUNTIME_DIR=/run/user/1000
XDG_SESSION_CLASS=user
XDG_SESSION_DESKTOP=ubuntu
XDG_SESSION_TYPE=wayland
XMAKE_PROGRAM_DIR=/home/<USER>/.local/share/xmake
XMAKE_PROGRAM_FILE=/home/<USER>/.local/bin/xmake
XMAKE_ROOTDIR=/home/<USER>/.local/bin
XMAKE_SHELL=bash
XMODIFIERS=@im=ibus
_=/usr/bin/colcon
