# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.1

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/code/ros2_control/src/ros-controls/ros2_controllers/ackermann_steering_controller

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/code/ros2_control/build/ackermann_steering_controller

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running tests..."
	/usr/bin/ctest $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test
.PHONY : test/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	cd /home/<USER>/code/ros2_control/build/ackermann_steering_controller && $(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/code/ros2_control/build/ackermann_steering_controller/CMakeFiles /home/<USER>/code/ros2_control/build/ackermann_steering_controller/gmock//CMakeFiles/progress.marks
	cd /home/<USER>/code/ros2_control/build/ackermann_steering_controller && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 gmock/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/code/ros2_control/build/ackermann_steering_controller/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /home/<USER>/code/ros2_control/build/ackermann_steering_controller && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 gmock/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /home/<USER>/code/ros2_control/build/ackermann_steering_controller && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 gmock/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /home/<USER>/code/ros2_control/build/ackermann_steering_controller && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 gmock/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /home/<USER>/code/ros2_control/build/ackermann_steering_controller && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
gmock/CMakeFiles/gmock.dir/rule:
	cd /home/<USER>/code/ros2_control/build/ackermann_steering_controller && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 gmock/CMakeFiles/gmock.dir/rule
.PHONY : gmock/CMakeFiles/gmock.dir/rule

# Convenience name for target.
gmock: gmock/CMakeFiles/gmock.dir/rule
.PHONY : gmock

# fast build rule for target.
gmock/fast:
	cd /home/<USER>/code/ros2_control/build/ackermann_steering_controller && $(MAKE) $(MAKESILENT) -f gmock/CMakeFiles/gmock.dir/build.make gmock/CMakeFiles/gmock.dir/build
.PHONY : gmock/fast

# Convenience name for target.
gmock/CMakeFiles/gmock_main.dir/rule:
	cd /home/<USER>/code/ros2_control/build/ackermann_steering_controller && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 gmock/CMakeFiles/gmock_main.dir/rule
.PHONY : gmock/CMakeFiles/gmock_main.dir/rule

# Convenience name for target.
gmock_main: gmock/CMakeFiles/gmock_main.dir/rule
.PHONY : gmock_main

# fast build rule for target.
gmock_main/fast:
	cd /home/<USER>/code/ros2_control/build/ackermann_steering_controller && $(MAKE) $(MAKESILENT) -f gmock/CMakeFiles/gmock_main.dir/build.make gmock/CMakeFiles/gmock_main.dir/build
.PHONY : gmock_main/fast

opt/ros/humble/src/gtest_vendor/src/gtest-all.o: opt/ros/humble/src/gtest_vendor/src/gtest-all.cc.o
.PHONY : opt/ros/humble/src/gtest_vendor/src/gtest-all.o

# target to build an object file
opt/ros/humble/src/gtest_vendor/src/gtest-all.cc.o:
	cd /home/<USER>/code/ros2_control/build/ackermann_steering_controller && $(MAKE) $(MAKESILENT) -f gmock/CMakeFiles/gmock.dir/build.make gmock/CMakeFiles/gmock.dir/opt/ros/humble/src/gtest_vendor/src/gtest-all.cc.o
.PHONY : opt/ros/humble/src/gtest_vendor/src/gtest-all.cc.o

opt/ros/humble/src/gtest_vendor/src/gtest-all.i: opt/ros/humble/src/gtest_vendor/src/gtest-all.cc.i
.PHONY : opt/ros/humble/src/gtest_vendor/src/gtest-all.i

# target to preprocess a source file
opt/ros/humble/src/gtest_vendor/src/gtest-all.cc.i:
	cd /home/<USER>/code/ros2_control/build/ackermann_steering_controller && $(MAKE) $(MAKESILENT) -f gmock/CMakeFiles/gmock.dir/build.make gmock/CMakeFiles/gmock.dir/opt/ros/humble/src/gtest_vendor/src/gtest-all.cc.i
.PHONY : opt/ros/humble/src/gtest_vendor/src/gtest-all.cc.i

opt/ros/humble/src/gtest_vendor/src/gtest-all.s: opt/ros/humble/src/gtest_vendor/src/gtest-all.cc.s
.PHONY : opt/ros/humble/src/gtest_vendor/src/gtest-all.s

# target to generate assembly for a file
opt/ros/humble/src/gtest_vendor/src/gtest-all.cc.s:
	cd /home/<USER>/code/ros2_control/build/ackermann_steering_controller && $(MAKE) $(MAKESILENT) -f gmock/CMakeFiles/gmock.dir/build.make gmock/CMakeFiles/gmock.dir/opt/ros/humble/src/gtest_vendor/src/gtest-all.cc.s
.PHONY : opt/ros/humble/src/gtest_vendor/src/gtest-all.cc.s

src/gmock-all.o: src/gmock-all.cc.o
.PHONY : src/gmock-all.o

# target to build an object file
src/gmock-all.cc.o:
	cd /home/<USER>/code/ros2_control/build/ackermann_steering_controller && $(MAKE) $(MAKESILENT) -f gmock/CMakeFiles/gmock.dir/build.make gmock/CMakeFiles/gmock.dir/src/gmock-all.cc.o
.PHONY : src/gmock-all.cc.o

src/gmock-all.i: src/gmock-all.cc.i
.PHONY : src/gmock-all.i

# target to preprocess a source file
src/gmock-all.cc.i:
	cd /home/<USER>/code/ros2_control/build/ackermann_steering_controller && $(MAKE) $(MAKESILENT) -f gmock/CMakeFiles/gmock.dir/build.make gmock/CMakeFiles/gmock.dir/src/gmock-all.cc.i
.PHONY : src/gmock-all.cc.i

src/gmock-all.s: src/gmock-all.cc.s
.PHONY : src/gmock-all.s

# target to generate assembly for a file
src/gmock-all.cc.s:
	cd /home/<USER>/code/ros2_control/build/ackermann_steering_controller && $(MAKE) $(MAKESILENT) -f gmock/CMakeFiles/gmock.dir/build.make gmock/CMakeFiles/gmock.dir/src/gmock-all.cc.s
.PHONY : src/gmock-all.cc.s

src/gmock_main.o: src/gmock_main.cc.o
.PHONY : src/gmock_main.o

# target to build an object file
src/gmock_main.cc.o:
	cd /home/<USER>/code/ros2_control/build/ackermann_steering_controller && $(MAKE) $(MAKESILENT) -f gmock/CMakeFiles/gmock_main.dir/build.make gmock/CMakeFiles/gmock_main.dir/src/gmock_main.cc.o
.PHONY : src/gmock_main.cc.o

src/gmock_main.i: src/gmock_main.cc.i
.PHONY : src/gmock_main.i

# target to preprocess a source file
src/gmock_main.cc.i:
	cd /home/<USER>/code/ros2_control/build/ackermann_steering_controller && $(MAKE) $(MAKESILENT) -f gmock/CMakeFiles/gmock_main.dir/build.make gmock/CMakeFiles/gmock_main.dir/src/gmock_main.cc.i
.PHONY : src/gmock_main.cc.i

src/gmock_main.s: src/gmock_main.cc.s
.PHONY : src/gmock_main.s

# target to generate assembly for a file
src/gmock_main.cc.s:
	cd /home/<USER>/code/ros2_control/build/ackermann_steering_controller && $(MAKE) $(MAKESILENT) -f gmock/CMakeFiles/gmock_main.dir/build.make gmock/CMakeFiles/gmock_main.dir/src/gmock_main.cc.s
.PHONY : src/gmock_main.cc.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... test"
	@echo "... gmock"
	@echo "... gmock_main"
	@echo "... opt/ros/humble/src/gtest_vendor/src/gtest-all.o"
	@echo "... opt/ros/humble/src/gtest_vendor/src/gtest-all.i"
	@echo "... opt/ros/humble/src/gtest_vendor/src/gtest-all.s"
	@echo "... src/gmock-all.o"
	@echo "... src/gmock-all.i"
	@echo "... src/gmock-all.s"
	@echo "... src/gmock_main.o"
	@echo "... src/gmock_main.i"
	@echo "... src/gmock_main.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /home/<USER>/code/ros2_control/build/ackermann_steering_controller && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

