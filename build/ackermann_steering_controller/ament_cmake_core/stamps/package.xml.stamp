<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>a<PERSON>mann_steering_controller</name>
  <version>2.49.1</version>
  <description>Steering controller for Ackermann kinematics. Rear fixed wheels are powering the vehicle and front wheels are steering it.</description>

  <maintainer email="<EMAIL>"><PERSON><PERSON></maintainer>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <maintainer email="christoph.fro<PERSON><PERSON>@ait.ac.at"><PERSON></maintainer>
  <maintainer email="<EMAIL>">Sai Kishor Kothakota</maintainer>

  <license>Apache License 2.0</license>

  <url type="website">https://control.ros.org</url>
  <url type="bugtracker">https://github.com/ros-controls/ros2_controllers/issues</url>
  <url type="repository">https://github.com/ros-controls/ros2_controllers/</url>

  <author email="<EMAIL>">Dr.-Ing. Denis Štogl</author>
  <author email="<EMAIL>">dr. sc. Tomislav Petkovic</author>

  <buildtool_depend>ament_cmake</buildtool_depend>

  <build_depend>generate_parameter_library</build_depend>

  <depend>backward_ros</depend>
  <depend>control_msgs</depend>
  <depend>controller_interface</depend>
  <depend>hardware_interface</depend>
  <depend>pluginlib</depend>
  <depend>rclcpp</depend>
  <depend>rclcpp_lifecycle</depend>
  <depend>std_srvs</depend>
  <depend>steering_controllers_library</depend>

  <test_depend>ament_cmake_gmock</test_depend>
  <test_depend>controller_manager</test_depend>
  <test_depend>hardware_interface_testing</test_depend>
  <test_depend>hardware_interface</test_depend>
  <test_depend>ros2_control_test_assets</test_depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
