set(_AMENT_PACKAGE_NAME "ackermann_steering_controller")
set(ackermann_steering_controller_VERSION "2.49.1")
set(a<PERSON>mann_steering_controller_MAINTAINER "<PERSON><PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>, <PERSON> <christo<PERSON>.<PERSON><PERSON><PERSON><PERSON>@ait.ac.at>, <PERSON> Kothakota <<EMAIL>>")
set(a<PERSON>mann_steering_controller_BUILD_DEPENDS "generate_parameter_library" "backward_ros" "control_msgs" "controller_interface" "hardware_interface" "pluginlib" "rclcpp" "rclcpp_lifecycle" "std_srvs" "steering_controllers_library")
set(ackermann_steering_controller_BUILDTOOL_DEPENDS "ament_cmake")
set(ackermann_steering_controller_BUILD_EXPORT_DEPENDS "backward_ros" "control_msgs" "controller_interface" "hardware_interface" "pluginlib" "rclcpp" "rclcpp_lifecycle" "std_srvs" "steering_controllers_library")
set(a<PERSON><PERSON>_steering_controller_BUILDTOOL_EXPORT_DEPENDS )
set(ackermann_steering_controller_EXEC_DEPENDS "backward_ros" "control_msgs" "controller_interface" "hardware_interface" "pluginlib" "rclcpp" "rclcpp_lifecycle" "std_srvs" "steering_controllers_library")
set(ackermann_steering_controller_TEST_DEPENDS "ament_cmake_gmock" "controller_manager" "hardware_interface_testing" "hardware_interface" "ros2_control_test_assets")
set(ackermann_steering_controller_GROUP_DEPENDS )
set(ackermann_steering_controller_MEMBER_OF_GROUPS )
set(ackermann_steering_controller_DEPRECATED "")
set(ackermann_steering_controller_EXPORT_TAGS)
list(APPEND ackermann_steering_controller_EXPORT_TAGS "<build_type>ament_cmake</build_type>")
